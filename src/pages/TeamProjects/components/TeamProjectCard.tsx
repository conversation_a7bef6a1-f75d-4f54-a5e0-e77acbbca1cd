import React from 'react';
import { BodyText, TextMuted, TextStrong, SecondaryButton } from '../../../components/ui';

interface TeamProjectCardProps {
  project: {
    _id: string;
    name: string;
    description?: string;
    createdAt: number;
    isArchived?: boolean;
    archivedAt?: number;
    isPubliclyShared?: boolean;
    shareSettings?: {
      accessCount?: number;
      lastAccessedAt?: number;
    };
    owner: {
      clerkUserId: string;
      role: string;
      firstName?: string;
      lastName?: string;
      email?: string;
      displayName: string;
    };
    customer?: {
      name: string;
      type: 'privat' | 'bedrift';
      address?: string;
      streetAddress?: string;
      postalCode?: string;
      city?: string;
      contactPerson?: string;
    } | null;
  };
  onClick: () => void;
  animationDelay?: string;
}

export const TeamProjectCard: React.FC<TeamProjectCardProps> = ({
  project,
  onClick,
  animationDelay = '0s',
}) => {
  // Format date for display
  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('nb-NO', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
    });
  };

  // Get customer display address
  const getCustomerAddress = () => {
    if (!project.customer) return null;
    
    // Use new structured address if available
    if (project.customer.streetAddress && project.customer.city) {
      return `${project.customer.streetAddress}, ${project.customer.city}`;
    }
    
    // Fallback to legacy address field
    return project.customer.address || null;
  };

  // Get customer display name
  const getCustomerDisplayName = () => {
    if (!project.customer) return null;
    
    if (project.customer.type === 'bedrift' && project.customer.contactPerson) {
      return `${project.customer.name} (${project.customer.contactPerson})`;
    }
    
    return project.customer.name;
  };

  // Handle card click
  const handleClick = () => {
    if (!project.isArchived) {
      onClick();
    }
  };

  // Handle key down for accessibility
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if ((e.key === 'Enter' || e.key === ' ') && !project.isArchived) {
      e.preventDefault();
      onClick();
    }
  };

  return (
    <div
      className={`
        ${project.isArchived ? 'card-modern shadow-medium opacity-75 border-dashed border-2 border-jobblogg-warning/40' : 'card-elevated hover-lift hover-glow cursor-pointer focus-ring-enhanced interactive-press touch-feedback mobile-focus'}
        animate-slide-up group
      `.trim().replace(/\s+/g, ' ')}
      style={{ animationDelay }}
      {...(!project.isArchived && {
        onClick: handleClick,
        onKeyDown: handleKeyDown,
        tabIndex: 0,
        role: "button",
        "aria-label": `Åpne prosjekt: ${project.name}`
      })}
    >
      {/* Project Header */}
      <div className="px-6 pt-6 relative">
        {/* Archived Badge */}
        {project.isArchived && (
          <div className="absolute top-8 right-8 z-10">
            <div className="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-bold bg-jobblogg-warning text-white shadow-lg border-2 border-white">
              <svg className="w-3 h-3 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M5 8l4 4 4-4m0 6l-4-4-4 4" />
              </svg>
              ARKIVERT
            </div>
          </div>
        )}

        {/* Sharing Status Badge */}
        {project.isPubliclyShared && !project.isArchived && (
          <div className="absolute top-8 right-8 z-10">
            <div className="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-bold bg-jobblogg-accent text-white shadow-lg border-2 border-white">
              <svg className="w-3 h-3 mr-1.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
              </svg>
              DELT
            </div>
          </div>
        )}

        {/* Project Icon Area */}
        <div className={`w-full h-48 rounded-xl flex items-center justify-center transition-all duration-300 ${
          project.isArchived
            ? 'bg-jobblogg-neutral-dark/50 group-hover:bg-jobblogg-neutral-dark/60'
            : 'gradient-blue-soft group-hover:gradient-card-hover'
        }`}>
          <div className={`p-4 backdrop-blur-sm rounded-2xl shadow-soft group-hover:shadow-medium transition-all duration-300 group-hover:scale-105 ${
            project.isArchived ? 'bg-white/60' : 'bg-white/90'
          }`}>
            <svg
              className="w-12 h-12 text-jobblogg-primary group-hover:text-jobblogg-primary-light transition-all duration-300 group-hover:rotate-3"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
              aria-hidden="true"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={1.5}
                d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
              />
            </svg>
          </div>
        </div>
      </div>

      {/* Project Content */}
      <div className="px-6 pb-6 pt-4 space-y-4">
        {/* Project Title and Description */}
        <div className="space-y-2">
          <TextStrong className="text-xl leading-tight line-clamp-2 group-hover:text-jobblogg-primary transition-colors duration-200">
            {project.name}
          </TextStrong>
          
          {project.description && (
            <TextMuted className="text-sm line-clamp-2 leading-relaxed">
              {project.description}
            </TextMuted>
          )}
        </div>

        {/* Owner Information */}
        <div className="flex items-center gap-3 p-3 bg-jobblogg-neutral rounded-lg">
          <div className="w-8 h-8 rounded-full bg-jobblogg-primary/10 flex items-center justify-center flex-shrink-0">
            <svg className="w-4 h-4 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
            </svg>
          </div>
          <div className="flex-1 min-w-0">
            <BodyText className="font-medium text-sm truncate">
              {project.owner.displayName}
            </BodyText>
            <TextMuted className="text-xs">
              {project.owner.role === 'administrator' ? 'Administrator' : 'Utførende'}
            </TextMuted>
          </div>
        </div>

        {/* Customer Information */}
        {project.customer && (
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <svg className="w-4 h-4 text-jobblogg-text-muted flex-shrink-0" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
              </svg>
              <BodyText className="font-medium text-sm truncate">
                {getCustomerDisplayName()}
              </BodyText>
              <span className={`inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium ${
                project.customer.type === 'bedrift'
                  ? 'bg-jobblogg-primary/10 text-jobblogg-primary'
                  : 'bg-jobblogg-accent/10 text-jobblogg-accent'
              }`}>
                {project.customer.type === 'bedrift' ? 'Bedrift' : 'Privat'}
              </span>
            </div>
            
            {getCustomerAddress() && (
              <div className="flex items-start gap-2">
                <svg className="w-4 h-4 text-jobblogg-text-muted flex-shrink-0 mt-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17.657 16.657L13.414 20.9a1.998 1.998 0 01-2.827 0l-4.244-4.243a8 8 0 1111.314 0z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 11a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
                <TextMuted className="text-sm line-clamp-2 leading-relaxed">
                  {getCustomerAddress()}
                </TextMuted>
              </div>
            )}
          </div>
        )}

        {/* Project Metadata */}
        <div className="flex items-center justify-between pt-2 border-t border-jobblogg-border">
          <div className="flex items-center gap-2">
            <svg className="w-4 h-4 text-jobblogg-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
            </svg>
            <TextMuted className="text-sm">
              {project.isArchived && project.archivedAt
                ? `Arkivert ${formatDate(project.archivedAt)}`
                : `Opprettet ${formatDate(project.createdAt)}`
              }
            </TextMuted>
          </div>

          {/* Sharing Statistics */}
          {project.isPubliclyShared && project.shareSettings?.accessCount && (
            <div className="flex items-center gap-1">
              <svg className="w-4 h-4 text-jobblogg-accent" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 12a3 3 0 11-6 0 3 3 0 016 0z" />
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z" />
              </svg>
              <TextMuted className="text-sm">
                {project.shareSettings.accessCount} visning{project.shareSettings.accessCount !== 1 ? 'er' : ''}
              </TextMuted>
            </div>
          )}
        </div>

        {/* Action Button for Non-Archived Projects */}
        {!project.isArchived && (
          <div className="pt-2" onClick={(e) => e.stopPropagation()}>
            <SecondaryButton
              onClick={onClick}
              className="w-full justify-center"
              size="sm"
            >
              Se prosjekt
            </SecondaryButton>
          </div>
        )}
      </div>
    </div>
  );
};
