import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { useQuery } from 'convex/react';
import { useUser } from '@clerk/clerk-react';
import { api } from '../../../convex/_generated/api';
import { 
  DashboardLayout, 
  StatsCard, 
  SecondaryButton,
  Heading2,
  BodyText,
  TextMuted,
  EmptyState,
  TextInput
} from '../../components/ui';
import { useUserRole } from '../../hooks/useUserRole';
import { TeamProjectCard } from './components/TeamProjectCard';

const TeamProjects: React.FC = () => {
  const { user } = useUser();
  const navigate = useNavigate();
  
  // State for filters and search
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedTeamMember, setSelectedTeamMember] = useState<string>('');
  const [includeArchived, setIncludeArchived] = useState(false);
  const [sortBy, setSortBy] = useState<'createdAt' | 'name' | 'lastActivity'>('createdAt');
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc');
  
  // Hooks for user role validation
  const { isAdministrator, isLoading: roleLoading } = useUserRole();
  
  // Query team projects overview
  const teamProjectsData = useQuery(
    api.teamManagement.getTeamProjectsOverview,
    user?.id && isAdministrator ? {
      requestedBy: user.id,
      includeArchived,
      teamMemberId: selectedTeamMember || undefined,
      searchQuery: searchQuery.trim() || undefined,
      sortBy,
      sortOrder
    } : "skip"
  );

  const isLoading = roleLoading || teamProjectsData === undefined;

  // Redirect if not administrator
  if (!roleLoading && !isAdministrator) {
    return (
      <DashboardLayout title="Ingen tilgang">
        <div className="text-center py-12">
          <div className="text-6xl mb-4">🔒</div>
          <Heading2 className="mb-4">Ingen tilgang</Heading2>
          <BodyText className="mb-6">
            Kun administratorer kan se prosjektoversikt.
          </BodyText>
          <SecondaryButton onClick={() => navigate('/team')}>
            Tilbake til teamadministrasjon
          </SecondaryButton>
        </div>
      </DashboardLayout>
    );
  }

  // Handle search input change
  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchQuery(e.target.value);
  };

  // Handle team member filter change
  const handleTeamMemberChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    setSelectedTeamMember(e.target.value);
  };

  // Handle sort change
  const handleSortChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const [newSortBy, newSortOrder] = e.target.value.split('-') as [typeof sortBy, typeof sortOrder];
    setSortBy(newSortBy);
    setSortOrder(newSortOrder);
  };

  // Get display name for team member
  const getTeamMemberDisplayName = (member: any) => {
    if (member.firstName && member.lastName) {
      return `${member.firstName} ${member.lastName}`;
    }
    return `Bruker ${member.clerkUserId.slice(-4)}`;
  };

  const projects = teamProjectsData?.projects || [];
  const teamMembers = teamProjectsData?.teamMembers || [];
  const totalCount = teamProjectsData?.totalCount || 0;

  return (
    <DashboardLayout
      title="Prosjektoversikt"
      subtitle="Administrer og overvåk alle teamprosjekter"
      headerActions={
        <div className="flex items-center gap-3">
          <SecondaryButton
            onClick={() => navigate('/team')}
            className="flex items-center gap-2"
          >
            <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M10 19l-7-7m0 0l7-7m-7 7h18" />
            </svg>
            Tilbake til teamadministrasjon
          </SecondaryButton>
        </div>
      }
      statsSection={
        !isLoading && (
          <div className="grid-stats">
            <StatsCard
              title="Totalt prosjekter"
              value={totalCount}
              variant="primary"
              animationDelay="0s"
              icon={
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
                </svg>
              }
            />
            
            <StatsCard
              title="Aktive prosjekter"
              value={projects.filter(p => !p.isArchived).length}
              variant="success"
              animationDelay="0.1s"
              icon={
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
                </svg>
              }
            />
            
            <StatsCard
              title="Delte prosjekter"
              value={projects.filter(p => p.isPubliclyShared).length}
              variant="accent"
              animationDelay="0.2s"
              icon={
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8.684 13.342C8.886 12.938 9 12.482 9 12c0-.482-.114-.938-.316-1.342m0 2.684a3 3 0 110-2.684m0 2.684l6.632 3.316m-6.632-6l6.632-3.316m0 0a3 3 0 105.367-2.684 3 3 0 00-5.367 2.684zm0 9.316a3 3 0 105.367 2.684 3 3 0 00-5.367-2.684z" />
                </svg>
              }
            />
            
            <StatsCard
              title="Teammedlemmer"
              value={teamMembers.length}
              variant="warning"
              animationDelay="0.3s"
              icon={
                <svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.196-2.121M9 20H4v-2a3 3 0 015.196-2.121m0 0a5.002 5.002 0 019.608 0M15 7a3 3 0 11-6 0 3 3 0 016 0z" />
                </svg>
              }
            />
          </div>
        )
      }
    >
      {isLoading ? (
        <div className="text-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
          <TextMuted>Laster prosjektoversikt...</TextMuted>
        </div>
      ) : (
        <div className="space-y-8">
          {/* Filters and Search Section */}
          <section className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6">
            <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-end">
              {/* Search Input */}
              <div className="flex-1 min-w-0">
                <label htmlFor="search" className="block text-sm font-medium text-jobblogg-text-strong mb-2">
                  Søk i prosjekter
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                    <svg className="h-5 w-5 text-jobblogg-text-muted" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                    </svg>
                  </div>
                  <TextInput
                    id="search"
                    type="text"
                    placeholder="Søk etter prosjektnavn, beskrivelse, kunde..."
                    value={searchQuery}
                    onChange={handleSearchChange}
                    className="pl-10"
                  />
                </div>
              </div>

              {/* Team Member Filter */}
              <div className="w-full lg:w-64">
                <label htmlFor="teamMember" className="block text-sm font-medium text-jobblogg-text-strong mb-2">
                  Filtrer etter teammedlem
                </label>
                <select
                  id="teamMember"
                  value={selectedTeamMember}
                  onChange={handleTeamMemberChange}
                  className="block w-full border border-jobblogg-border rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-jobblogg-primary focus:border-jobblogg-primary"
                >
                  <option value="">Alle teammedlemmer</option>
                  {teamMembers.map((member) => (
                    <option key={member.clerkUserId} value={member.clerkUserId}>
                      {getTeamMemberDisplayName(member)} ({member.role === 'administrator' ? 'Administrator' : 'Utførende'})
                    </option>
                  ))}
                </select>
              </div>

              {/* Sort Controls */}
              <div className="w-full lg:w-48">
                <label htmlFor="sort" className="block text-sm font-medium text-jobblogg-text-strong mb-2">
                  Sorter etter
                </label>
                <select
                  id="sort"
                  value={`${sortBy}-${sortOrder}`}
                  onChange={handleSortChange}
                  className="block w-full border border-jobblogg-border rounded-lg px-3 py-2 text-sm focus:ring-2 focus:ring-jobblogg-primary focus:border-jobblogg-primary"
                >
                  <option value="createdAt-desc">Nyeste først</option>
                  <option value="createdAt-asc">Eldste først</option>
                  <option value="name-asc">Navn A-Å</option>
                  <option value="name-desc">Navn Å-A</option>
                  <option value="lastActivity-desc">Siste aktivitet</option>
                </select>
              </div>
            </div>

            {/* Results Summary */}
            {(searchQuery || selectedTeamMember) && (
              <div className="mt-4 pt-4 border-t border-jobblogg-border">
                <TextMuted>
                  Viser {totalCount} prosjekt{totalCount !== 1 ? 'er' : ''}
                  {searchQuery && ` som matcher "${searchQuery}"`}
                  {selectedTeamMember && ` for ${getTeamMemberDisplayName(teamMembers.find(m => m.clerkUserId === selectedTeamMember))}`}
                </TextMuted>
              </div>
            )}
          </section>

          {/* Projects Grid */}
          <section>
            {projects.length === 0 ? (
              <EmptyState
                title="📋 Ingen prosjekter funnet"
                description={
                  searchQuery || selectedTeamMember
                    ? "Ingen prosjekter matcher de valgte filtrene. Prøv å justere søket eller filteret."
                    : "Det er ingen prosjekter å vise for teamet ennå."
                }
                actionLabel={searchQuery || selectedTeamMember ? "Tilbakestill filtre" : undefined}
                onAction={() => {
                  setSearchQuery('');
                  setSelectedTeamMember('');
                }}
              />
            ) : (
              <div className="grid-mobile-cards">
                {projects.map((project, index) => (
                  <TeamProjectCard
                    key={project._id}
                    project={project}
                    onClick={() => navigate(`/project/${project._id}`)}
                    animationDelay={`${index * 0.1}s`}
                  />
                ))}
              </div>
            )}
          </section>
        </div>
      )}
    </DashboardLayout>
  );
};

export default TeamProjects;
