import React from 'react';
import { SignIn as ClerkSignIn } from '@clerk/clerk-react';
import { Link, useSearchParams } from 'react-router-dom';
import { PrimaryButton } from '../../components/ui';

const SignIn: React.FC = () => {
  const [searchParams] = useSearchParams();
  const redirectUrl = searchParams.get('redirect') || '/';

  return (
    <div className="min-h-screen bg-white animate-fade-in">
      <div className="container mx-auto px-4 py-8 flex items-center justify-center min-h-screen">
        <div className="w-full max-w-md">
          {/* Modern Header */}
          <div className="text-center mb-12 animate-slide-up">
            <div className="w-16 h-16 mx-auto mb-6 bg-jobblogg-primary-soft rounded-full flex items-center justify-center">
              <svg className="w-8 h-8 text-jobblogg-primary" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <h1 className="text-4xl font-bold text-jobblogg-text-strong mb-3 bg-gradient-to-r from-jobblogg-primary to-jobblogg-accent bg-clip-text text-transparent">
              Velkommen tilbake! 👋
            </h1>
            <p className="text-body text-lg">
              Logg inn på JobbLogg-kontoen din og fortsett dokumenteringen
            </p>
          </div>

          {/* Modern Clerk SignIn Component */}
          {/* Clerk SignIn Component - Using centralized theme-aware appearance */}
          <div className="animate-scale-in" style={{ animationDelay: '200ms' }}>
            <ClerkSignIn
              fallbackRedirectUrl={redirectUrl}
              signUpUrl="/sign-up"
            />
          </div>

          {/* Modern Sign Up Link */}
          <div className="text-center mt-8 animate-slide-up" style={{ animationDelay: '400ms' }}>
            <div className="bg-jobblogg-neutral rounded-lg p-4">
              <p className="text-body mb-2">
                Har du ikke konto ennå? 🚀
              </p>
              <Link to="/sign-up">
                <PrimaryButton
                  variant="outline"
                  className="font-semibold w-full"
                  icon={
                    <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M18 9v3m0 0v3m0-3h3m-3 0h-3m-2-5a4 4 0 11-8 0 4 4 0 018 0zM3 20a6 6 0 0112 0v1H3v-1z" />
                    </svg>
                  }
                >
                  Opprett konto
                </PrimaryButton>
              </Link>
            </div>
          </div>

          {/* Modern Footer */}
          <div className="text-center mt-8 animate-slide-up" style={{ animationDelay: '600ms' }}>
            <div className="flex items-center justify-center gap-2 text-caption">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
              </svg>
              <span>JobbLogg - Dokumenter arbeidet ditt enkelt</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default SignIn;
