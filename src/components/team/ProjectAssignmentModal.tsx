import React, { useState } from 'react';
import { useMutation } from 'convex/react';
import { useUser } from '@clerk/clerk-react';
import { api } from '../../../convex/_generated/api';
import { useTeamMembers } from '../../hooks/useUserRole';
import {
  Modal,
  PrimaryButton,
  SecondaryButton,
  SelectInput,
  TextArea,
  FormError,
  Heading2,
  BodyText,
  TextMuted
} from '../ui';

interface ProjectAssignmentModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSuccess: () => void;
  projectId: string;
  projectName: string;
  currentAssignments?: Array<{
    assignedUserId: string;
    accessLevel: string;
    assignedUserRole: string;
  }>;
}

export const ProjectAssignmentModal: React.FC<ProjectAssignmentModalProps> = ({
  isOpen,
  onClose,
  onSuccess,
  projectId,
  projectName,
  currentAssignments = [],
}) => {
  const { user } = useUser();
  const { teamMembers } = useTeamMembers();
  
  const [selectedUserId, setSelectedUserId] = useState('');
  const [accessLevel, setAccessLevel] = useState<'owner' | 'collaborator' | 'viewer'>('collaborator');
  const [notes, setNotes] = useState('');
  const [error, setError] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);

  const assignProject = useMutation(api.teamManagement.assignProjectToUser);

  // Filter out already assigned users and current user
  const availableUsers = teamMembers.filter(member => 
    member.clerkUserId !== user?.id && 
    !currentAssignments.some(assignment => assignment.assignedUserId === member.clerkUserId)
  );

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!user?.id) {
      setError('Bruker ikke autentisert');
      return;
    }

    if (!selectedUserId) {
      setError('Velg en teammedlem å tildele prosjektet til');
      return;
    }

    setIsSubmitting(true);
    setError('');

    try {
      await assignProject({
        projectId: projectId as any,
        assignedUserId: selectedUserId,
        assignedBy: user.id,
        accessLevel,
        notes: notes.trim() || undefined,
      });

      // Reset form
      setSelectedUserId('');
      setAccessLevel('collaborator');
      setNotes('');
      
      onSuccess();
    } catch (err) {
      console.error('Failed to assign project:', err);
      setError(err instanceof Error ? err.message : 'Kunne ikke tildele prosjekt');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      setSelectedUserId('');
      setAccessLevel('collaborator');
      setNotes('');
      setError('');
      onClose();
    }
  };

  const getAccessLevelDescription = (level: string) => {
    switch (level) {
      case 'owner':
        return 'Full kontroll - kan redigere, slette og tildele prosjektet til andre';
      case 'collaborator':
        return 'Kan redigere prosjektet og legge til loggoppføringer';
      case 'viewer':
        return 'Kun lesetilgang til prosjektet';
      default:
        return '';
    }
  };

  return (
    <Modal isOpen={isOpen} onClose={handleClose} size="md">
      <div className="p-6">
        <div className="mb-6">
          <Heading2 className="mb-2">Tildel prosjekt</Heading2>
          <BodyText className="text-jobblogg-text-medium">
            Tildel "{projectName}" til en teammedlem
          </BodyText>
        </div>

        {availableUsers.length === 0 ? (
          <div className="text-center py-8">
            <div className="text-4xl mb-4">👥</div>
            <BodyText className="mb-4">
              Alle teammedlemmer har allerede tilgang til dette prosjektet.
            </BodyText>
            <SecondaryButton onClick={handleClose}>
              Lukk
            </SecondaryButton>
          </div>
        ) : (
          <form onSubmit={handleSubmit} className="space-y-6">
            {/* Team Member Selection */}
            <div className="space-y-2">
              <label htmlFor="teamMember" className="block text-sm font-medium text-jobblogg-text-strong">
                Teammedlem <span className="text-jobblogg-error">*</span>
              </label>
              <SelectInput
                id="teamMember"
                value={selectedUserId}
                onChange={(e) => setSelectedUserId(e.target.value)}
                disabled={isSubmitting}
                required
                placeholder="Velg teammedlem..."
                options={[
                  ...availableUsers.map((member) => {
                    // Get display name with improved fallback logic
                    const getDisplayName = () => {
                      if (member.firstName && member.lastName) {
                        return `${member.firstName} ${member.lastName}`;
                      }
                      if (member.firstName) {
                        return member.firstName;
                      }
                      if (member.email) {
                        return member.email.split('@')[0];
                      }
                      return `Teammedlem ${member.clerkUserId.slice(-4)}`;
                    };

                    return {
                      value: member.clerkUserId,
                      label: `${member.role === 'administrator' ? 'Administrator' : 'Utførende'} - ${getDisplayName()}`
                    };
                  })
                ]}
              />
            </div>

            {/* Access Level Selection */}
            <div className="space-y-2">
              <label htmlFor="accessLevel" className="block text-sm font-medium text-jobblogg-text-strong">
                Tilgangsnivå <span className="text-jobblogg-error">*</span>
              </label>
              <SelectInput
                id="accessLevel"
                value={accessLevel}
                onChange={(e) => setAccessLevel(e.target.value as 'owner' | 'collaborator' | 'viewer')}
                disabled={isSubmitting}
                required
                options={[
                  { value: 'collaborator', label: 'Samarbeidspartner' },
                  { value: 'viewer', label: 'Leser' },
                  { value: 'owner', label: 'Eier' }
                ]}
              />
              <TextMuted className="text-sm mt-1">
                {getAccessLevelDescription(accessLevel)}
              </TextMuted>
            </div>

            {/* Notes Field */}
            <div className="space-y-2">
              <label htmlFor="notes" className="block text-sm font-medium text-jobblogg-text-strong">
                Notater (valgfritt)
              </label>
              <TextArea
                id="notes"
                value={notes}
                onChange={(e) => setNotes(e.target.value)}
                placeholder="Legg til notater om tildelingen..."
                disabled={isSubmitting}
                rows={3}
              />
            </div>

            {/* Current Assignments Info */}
            {currentAssignments.length > 0 && (
              <div className="bg-jobblogg-neutral/50 rounded-lg p-4">
                <BodyText className="font-medium mb-2">
                  Nåværende tildelinger:
                </BodyText>
                <div className="space-y-1">
                  {currentAssignments.map((assignment, index) => (
                    <TextMuted key={index} className="text-sm">
                      • {assignment.assignedUserRole === 'administrator' ? 'Administrator' : 'Utførende'} ({assignment.assignedUserId.slice(-4)}) - {
                        assignment.accessLevel === 'owner' ? 'Eier' :
                        assignment.accessLevel === 'collaborator' ? 'Samarbeidspartner' : 'Leser'
                      }
                    </TextMuted>
                  ))}
                </div>
              </div>
            )}

            {/* Error Message */}
            {error && (
              <FormError>{error}</FormError>
            )}

            {/* Action Buttons */}
            <div className="flex items-center justify-end gap-3 pt-4 border-t border-jobblogg-border">
              <SecondaryButton
                type="button"
                onClick={handleClose}
                disabled={isSubmitting}
              >
                Avbryt
              </SecondaryButton>
              
              <PrimaryButton
                type="submit"
                disabled={isSubmitting || !selectedUserId}
                icon={isSubmitting ? (
                  <svg className="w-5 h-5 animate-spin" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                ) : (
                  <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                  </svg>
                )}
              >
                {isSubmitting ? 'Tildeler...' : 'Tildel prosjekt'}
              </PrimaryButton>
            </div>
          </form>
        )}
      </div>
    </Modal>
  );
};
