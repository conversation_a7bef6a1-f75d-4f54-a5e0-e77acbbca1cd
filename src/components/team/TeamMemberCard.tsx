import React, { useState } from 'react';
import { BodyText, TextMuted, SecondaryButton } from '../ui';
import { TeamMemberDetailsModal } from './TeamMemberDetailsModal';

interface TeamMember {
  _id: string;
  clerkUserId: string;
  role: string;
  invitationStatus?: string;
  invitedAt?: number;
  acceptedAt?: number;
  createdAt: number;
  // Invitation data for display
  firstName?: string;
  lastName?: string;
  email?: string;
  phone?: string;
  // Activity data for "sist logget inn"
  lastLoginAt?: number;
  lastActivityAt?: number;
  // Blocking status
  isBlocked?: boolean;
  blockedAt?: number;
  blockedBy?: string;
  blockedReason?: string;
}

interface TeamMemberCardProps {
  member: TeamMember;
  currentUserId?: string;
  onMemberUpdate?: () => void;
}

export const TeamMemberCard: React.FC<TeamMemberCardProps> = ({
  member,
  currentUserId,
  onMemberUpdate,
}) => {
  const [showDetailsModal, setShowDetailsModal] = useState(false);
  const isCurrentUser = member.clerkUserId === currentUserId;
  const roleLabel = member.role === 'administrator' ? 'Administrator' : 'Utførende';

  // Display name logic
  const getDisplayName = () => {
    if (isCurrentUser) return 'Deg';
    if (member.firstName && member.lastName) {
      return `${member.firstName} ${member.lastName}`;
    }
    return `Bruker ${member.clerkUserId.slice(-4)}`;
  };
  
  // Format dates
  const formatDate = (timestamp: number) => {
    return new Date(timestamp).toLocaleDateString('nb-NO', {
      day: 'numeric',
      month: 'short',
      year: 'numeric',
    });
  };

  const joinedDate = member.acceptedAt || member.createdAt;

  return (
    <div className="bg-white rounded-xl border border-jobblogg-border shadow-soft p-6 hover:shadow-medium transition-all duration-200">
      <div className="flex items-start justify-between">
        <div className="flex items-start gap-4 flex-1">
          {/* Avatar placeholder with status indicator */}
          <div className={`w-12 h-12 rounded-full flex items-center justify-center flex-shrink-0 relative ${
            member.isBlocked
              ? 'bg-jobblogg-error/10'
              : 'bg-jobblogg-primary/10'
          }`}>
            <svg className={`w-6 h-6 ${
              member.isBlocked
                ? 'text-jobblogg-error'
                : 'text-jobblogg-primary'
            }`} fill="none" stroke="currentColor" viewBox="0 0 24 24">
              {member.isBlocked ? (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 15v2m0 0v2m0-2h2m-2 0H10m4-6V9a4 4 0 10-8 0v2m0 0v2a2 2 0 002 2h4a2 2 0 002-2v-2" />
              ) : (
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              )}
            </svg>

            {/* Blocked indicator */}
            {member.isBlocked && (
              <div className="absolute -top-1 -right-1 w-4 h-4 bg-jobblogg-error rounded-full flex items-center justify-center">
                <svg className="w-2.5 h-2.5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={3} d="M6 18L18 6M6 6l12 12" />
                </svg>
              </div>
            )}
          </div>

          <div className="flex-1 min-w-0">
            <div className="flex items-center gap-2 mb-1">
              <BodyText className="font-medium truncate">
                {getDisplayName()}
              </BodyText>
              
              {/* Role badge */}
              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                member.role === 'administrator'
                  ? 'bg-jobblogg-primary/10 text-jobblogg-primary border border-jobblogg-primary/20'
                  : 'bg-jobblogg-accent/10 text-jobblogg-accent border border-jobblogg-accent/20'
              }`}>
                {roleLabel}
              </span>

              {/* Blocked status badge */}
              {member.isBlocked && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-jobblogg-error/10 text-jobblogg-error border border-jobblogg-error/20">
                  Sperret
                </span>
              )}

              {/* Current user indicator */}
              {isCurrentUser && (
                <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-jobblogg-success/10 text-jobblogg-success border border-jobblogg-success/20">
                  Du
                </span>
              )}
            </div>

            <div className="space-y-1">
              <TextMuted className="text-sm">
                Ble med {formatDate(joinedDate)}
              </TextMuted>
              
              {member.invitationStatus === 'accepted' && member.invitedAt && (
                <TextMuted className="text-sm">
                  Invitert {formatDate(member.invitedAt)}
                </TextMuted>
              )}

              {!member.invitationStatus && (
                <TextMuted className="text-sm">
                  Opprinnelig medlem
                </TextMuted>
              )}
            </div>
          </div>
        </div>

        {/* Actions */}
        {!isCurrentUser && (
          <div className="flex items-center gap-2 ml-4">
            <SecondaryButton
              size="sm"
              onClick={() => setShowDetailsModal(true)}
            >
              Detaljer
            </SecondaryButton>
          </div>
        )}
      </div>

      {/* Member stats or additional info could go here */}
      <div className="mt-4 pt-4 border-t border-jobblogg-border/50">
        <div className="flex items-center justify-between text-sm">
          <TextMuted>
            Status: {!member.invitationStatus || member.invitationStatus === 'accepted' ? 'Aktiv' : 'Ventende'}
          </TextMuted>
          
          {member.role === 'administrator' && (
            <div className="flex items-center gap-1 text-jobblogg-primary">
              <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m5.618-4.016A11.955 11.955 0 0112 2.944a11.955 11.955 0 01-8.618 3.04A12.02 12.02 0 003 9c0 5.591 3.824 10.29 9 11.622 5.176-1.332 9-6.031 9-11.622 0-1.042-.133-2.052-.382-3.016z" />
              </svg>
              <span className="text-xs font-medium">Full tilgang</span>
            </div>
          )}
        </div>
      </div>

      {/* Team Member Details Modal */}
      <TeamMemberDetailsModal
        isOpen={showDetailsModal}
        onClose={() => setShowDetailsModal(false)}
        member={member}
        onMemberUpdate={onMemberUpdate}
      />
    </div>
  );
};
