import React from 'react';

interface DangerButtonProps {
  /** Button content */
  children: React.ReactNode;
  /** Click handler */
  onClick?: () => void;
  /** Button type for forms */
  type?: 'button' | 'submit' | 'reset';
  /** Disabled state */
  disabled?: boolean;
  /** Loading state */
  loading?: boolean;
  /** Button size */
  size?: 'sm' | 'md' | 'lg';
  /** Button variant */
  variant?: 'solid' | 'outline';
  /** Icon element */
  icon?: React.ReactNode;
  /** Additional CSS classes */
  className?: string;
  /** Full width button */
  fullWidth?: boolean;
}

/**
 * Danger button component for destructive actions
 * Uses red color scheme to indicate dangerous/destructive actions
 */
export const DangerButton: React.FC<DangerButtonProps> = ({
  children,
  onClick,
  type = 'button',
  disabled = false,
  loading = false,
  size = 'md',
  variant = 'solid',
  icon,
  className = '',
  fullWidth = false,
}) => {
  // Size classes
  const sizeClasses = {
    sm: 'px-3 py-2 text-sm',
    md: 'px-4 py-2.5 text-base',
    lg: 'px-6 py-3 text-lg',
  };

  // Variant classes
  const variantClasses = {
    solid: 'bg-jobblogg-error text-white border border-jobblogg-error hover:bg-jobblogg-error/90 focus:ring-jobblogg-error/20',
    outline: 'bg-transparent text-jobblogg-error border border-jobblogg-error hover:bg-jobblogg-error/5 focus:ring-jobblogg-error/20',
  };

  // Disabled classes
  const disabledClasses = disabled || loading
    ? 'opacity-50 cursor-not-allowed'
    : 'hover:shadow-medium active:transform active:scale-[0.98]';

  // Width classes
  const widthClasses = fullWidth ? 'w-full' : '';

  const baseClasses = `
    inline-flex items-center justify-center gap-2 
    font-medium rounded-lg transition-all duration-200
    focus:outline-none focus:ring-2 focus:ring-offset-2
    min-h-[44px] touch-manipulation
    ${sizeClasses[size]}
    ${variantClasses[variant]}
    ${disabledClasses}
    ${widthClasses}
    ${className}
  `.trim().replace(/\s+/g, ' ');

  return (
    <button
      type={type}
      onClick={onClick}
      disabled={disabled || loading}
      className={baseClasses}
    >
      {loading ? (
        <svg className="w-5 h-5 animate-spin" fill="none" viewBox="0 0 24 24">
          <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
          <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
        </svg>
      ) : icon}
      
      <span className={loading ? 'opacity-75' : ''}>
        {children}
      </span>
    </button>
  );
};
