import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import { ConvexReactClient } from 'convex/react'
import { ConvexProviderWithClerk } from 'convex/react-clerk'
import { ClerkProvider, useAuth } from '@clerk/clerk-react'
import { nbNO } from '@clerk/localizations'
import './index.css'
import App from './App.tsx'
import { clerkAppearance } from './styles/clerkAppearance'

// Combine official Norwegian localization with our custom additions
const jobbloggLocalization = {
  ...nbNO,
  // Add our custom UserButton menu items
  userButtonPopoverActionButton__manageAccount: 'Administrer konto',
  userButtonPopoverActionButton__signOut: 'Logg ut',
  userButtonPopoverActionButton__companyProfile: 'Bedriftsprofil',
};
import { PerformanceMonitor } from './utils/lazyLoading'

const convex = new ConvexReactClient(import.meta.env.VITE_CONVEX_URL as string)
const clerkPubKey = import.meta.env.VITE_CLERK_PUBLISHABLE_KEY as string

if (!clerkPubKey) {
  throw new Error("Missing Clerk Publishable Key")
}

// Register service worker for PWA functionality
if ('serviceWorker' in navigator) {
  window.addEventListener('load', () => {
    navigator.serviceWorker.register('/sw.js')
      .then((registration) => {
        console.log('[PWA] Service Worker registered successfully:', registration.scope);
      })
      .catch((error) => {
        console.log('[PWA] Service Worker registration failed:', error);
      });
  });
}

// Initialize performance monitoring
PerformanceMonitor.monitorWebVitals();



createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <ClerkProvider
      publishableKey={clerkPubKey}
      appearance={clerkAppearance}
      localization={jobbloggLocalization}
      organizationMode="optional"
    >
      <ConvexProviderWithClerk client={convex} useAuth={useAuth}>
        <App />
      </ConvexProviderWithClerk>
    </ClerkProvider>
  </StrictMode>,
)
