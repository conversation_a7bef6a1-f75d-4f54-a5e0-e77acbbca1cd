@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800;900&display=swap');

@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  html {
    font-family: 'Inter', system-ui, sans-serif;
  }

  body {
    @apply transition-colors duration-200;
  }
}

@layer components {
 
 /* ===== SURFACE AND HIGHLIGHT SUPPORT CLASSES ===== */

/* New background surface – for sections, forms, alerts, cards */
.bg-surface {
  @apply bg-jobblogg-surface;
}

/* Section container with surface background and subtle border */
.section-surface {
  @apply bg-jobblogg-surface rounded-xl border border-jobblogg-border p-6 shadow-soft;
}

/* Emphasize elements using the highlight color (e.g., tags, status indicators) */
.text-highlight {
  @apply text-jobblogg-highlight;
}

.bg-highlight-soft {
  @apply bg-jobblogg-highlight/10 text-jobblogg-highlight;
}

/* Tags, badges, and chips styled with the highlight color */
.tag-highlight {
  @apply inline-flex items-center px-3 py-1 text-sm font-medium rounded-full bg-highlight-soft;
}
  
  /* Modern button system - Flat design with enhanced accessibility */
  .btn-modern {
    @apply inline-flex items-center justify-center gap-2 transition-all duration-200
           font-medium rounded-xl px-6 py-3 min-h-[44px]
           focus:outline-none focus:ring-2 focus:ring-jobblogg-primary focus:ring-offset-2
           disabled:opacity-50 disabled:cursor-not-allowed
           shadow-soft hover:shadow-medium whitespace-nowrap;
  }

  /* Primary button - Main CTA */
  .btn-primary-solid {
    @apply btn-modern bg-jobblogg-primary text-white
           hover:bg-jobblogg-primary-light active:bg-jobblogg-primary-dark
           focus:ring-jobblogg-primary;
    color: white !important; /* Ensure white text color for contrast */
  }

  /* Secondary button - Alternative actions */
  .btn-secondary-solid {
    @apply btn-modern bg-jobblogg-accent text-white
           hover:bg-jobblogg-accent-light active:bg-jobblogg-accent-dark
           focus:ring-jobblogg-accent;
  }

  /* Outline button - Secondary actions */
  .btn-outline {
    @apply btn-modern border-2 border-jobblogg-primary text-jobblogg-primary bg-white
           hover:bg-jobblogg-primary hover:text-white active:bg-jobblogg-primary-dark
           focus:ring-jobblogg-primary;
  }

  /* Ghost button - Subtle actions */
  .btn-ghost-enhanced {
    @apply btn-modern text-jobblogg-text-medium bg-transparent
           hover:text-jobblogg-text-strong hover:bg-jobblogg-neutral
           focus:ring-jobblogg-primary shadow-none hover:shadow-soft;
  }

  /* Soft button variants */
  .btn-soft {
    @apply btn-modern bg-jobblogg-primary-soft text-jobblogg-primary
           hover:bg-jobblogg-primary hover:text-white
           focus:ring-jobblogg-primary;
  }

  .btn-success-soft {
    @apply btn-modern bg-jobblogg-accent-soft text-jobblogg-accent
           hover:bg-jobblogg-accent hover:text-white
           focus:ring-jobblogg-accent;
  }

  .btn-warning-soft {
    @apply btn-modern bg-jobblogg-warning-soft text-jobblogg-warning
           hover:bg-jobblogg-warning hover:text-jobblogg-text-strong
           focus:ring-jobblogg-warning;
  }

  .btn-error-soft {
    @apply btn-modern bg-jobblogg-error-soft text-jobblogg-error
           hover:bg-jobblogg-error hover:text-white
           focus:ring-jobblogg-error;
  }

  /* Responsive button utilities */
  .btn-responsive {
    @apply text-sm sm:text-base px-3 py-2 sm:px-6 sm:py-3 min-w-[100px] sm:min-w-[120px];
  }

  .btn-responsive-lg {
    @apply text-base sm:text-lg px-4 py-2.5 sm:px-8 sm:py-4 min-w-[120px] sm:min-w-[160px];
  }

  .btn-wizard {
    @apply btn-modern btn-responsive whitespace-nowrap flex-shrink-0;
  }

  .btn-wizard-lg {
    @apply btn-modern btn-responsive-lg whitespace-nowrap flex-shrink-0;
  }

  /* Modern card system - Flat design with subtle elevation */
  .card-modern {
    @apply bg-white rounded-xl border border-jobblogg-border p-6
           transition-all duration-200 shadow-soft;
  }

  .card-elevated {
    @apply card-modern shadow-medium hover:shadow-large
           hover:-translate-y-0.5 cursor-pointer;
  }

  .card-hover {
    @apply card-elevated;
  }

  /* Modern input system - Enhanced accessibility and flat design */
  .input-modern {
    @apply w-full bg-white border border-jobblogg-border text-jobblogg-text-strong
           placeholder:text-jobblogg-text-muted rounded-xl px-4 py-3 min-h-[44px]
           transition-all duration-200
           focus:outline-none focus:ring-2 focus:ring-jobblogg-primary focus:border-jobblogg-primary
           hover:border-jobblogg-primary
           disabled:bg-jobblogg-neutral disabled:text-jobblogg-text-muted disabled:cursor-not-allowed;
  }

  /* Textarea styling - Consistent with input design */
  .textarea-modern {
    @apply input-modern resize-y min-h-[120px] py-3;
  }



  .input-bordered {
    @apply input-modern;
  }

  /* Alert components - Enhanced contrast for WCAG AA compliance */
  .alert-success {
    @apply bg-jobblogg-accent-soft border border-jobblogg-accent text-jobblogg-text-strong rounded-xl p-4 shadow-sm;
  }

  .alert-warning {
    @apply bg-jobblogg-warning-soft border border-jobblogg-warning text-jobblogg-text-strong rounded-xl p-4 shadow-sm;
  }

  .alert-error {
    @apply bg-jobblogg-error-soft border border-jobblogg-error text-jobblogg-text-strong rounded-xl p-4 shadow-sm;
  }

  .alert-info {
    @apply bg-jobblogg-primary-soft border border-jobblogg-primary text-jobblogg-text-strong rounded-xl p-4 shadow-sm;
  }

  /* Alert icon containers with proper contrast */
  .alert-icon-success {
    @apply bg-jobblogg-accent text-white rounded-full p-2 flex-shrink-0;
  }

  .alert-icon-warning {
    @apply bg-jobblogg-warning text-jobblogg-text-strong rounded-full p-2 flex-shrink-0;
  }

  .alert-icon-error {
    @apply bg-jobblogg-error text-white rounded-full p-2 flex-shrink-0;
  }

  .alert-icon-info {
    @apply bg-jobblogg-primary text-white rounded-full p-2 flex-shrink-0;
  }

  /* Loading skeleton */
  .skeleton {
    @apply animate-pulse bg-jobblogg-neutral rounded-xl;
  }

  /* Modern typography hierarchy - 2025 design system */
  .text-heading-1 {
    @apply text-4xl sm:text-5xl text-jobblogg-text-strong font-bold leading-tight mb-4 break-words;
  }

  .text-heading-2 {
    @apply text-3xl sm:text-4xl text-jobblogg-text-strong font-semibold leading-tight mb-3 break-words;
  }

  .text-heading-3 {
    @apply text-2xl sm:text-3xl text-jobblogg-text-strong font-semibold leading-snug mb-2 break-words;
  }

  .text-body {
    @apply text-base text-jobblogg-text-medium leading-relaxed;
  }

  .text-small {
    @apply text-sm text-jobblogg-text-medium leading-normal;
  }

  .text-caption {
    @apply text-xs text-jobblogg-text-muted leading-normal;
  }

  /* Text color utilities - WCAG AA compliant */
  .text-strong {
    @apply text-jobblogg-text-strong;
  }

  .text-medium {
    @apply text-jobblogg-text-medium;
  }

  .text-muted {
    @apply text-jobblogg-text-muted;
  }



  /* Modern layout system - Mobile-first responsive design */
  .container-section {
    @apply py-8 px-4 sm:py-12 sm:px-6 lg:px-8;
  }

  .container-content {
    @apply max-w-7xl mx-auto;
  }

  /* Enhanced container system for different content types */
  .container-narrow {
    @apply max-w-2xl mx-auto px-4 sm:px-6;
  }

  .container-medium {
    @apply max-w-4xl mx-auto px-4 sm:px-6;
  }

  .container-wide {
    @apply max-w-6xl mx-auto px-4 sm:px-6;
  }

  .container-full {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }

  /* Enhanced spacing system for modern design */
  .space-section {
    @apply mb-12 sm:mb-16 lg:mb-20;
  }

  .space-component {
    @apply mb-6 sm:mb-8;
  }

  .space-element {
    @apply mb-3 sm:mb-4;
  }

  /* Page layout utilities */
  .page-header {
    @apply mb-8 sm:mb-12;
  }

  .page-content {
    @apply space-y-6 sm:space-y-8;
  }

  /* Mobile-first touch targets - WCAG AA compliant */
  .touch-target {
    @apply min-h-[44px] min-w-[44px] flex items-center justify-center;
  }

  .touch-target-large {
    @apply min-h-[48px] min-w-[48px] flex items-center justify-center;
  }

  /* Enhanced mobile-first responsive spacing */
  .mobile-padding {
    @apply px-4 sm:px-6 md:px-8 lg:px-12;
  }

  .mobile-margin {
    @apply mx-4 sm:mx-6 md:mx-8 lg:mx-12;
  }

  .mobile-gap {
    @apply gap-3 sm:gap-4 md:gap-6 lg:gap-8;
  }

  /* Modern grid system - Enhanced responsive grids */
  .grid-auto-fit {
    @apply grid gap-6 grid-cols-[repeat(auto-fit,minmax(280px,1fr))];
  }

  .grid-auto-fill {
    @apply grid gap-6 grid-cols-[repeat(auto-fill,minmax(280px,1fr))];
  }

  .grid-responsive {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 mobile-gap;
  }

  .grid-stats {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 mobile-gap;
  }

  /* Mobile-optimized card grids */
  .grid-mobile-cards {
    @apply grid grid-cols-1 sm:grid-cols-2 xl:grid-cols-3 mobile-gap;
  }

  .grid-mobile-first {
    @apply grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 mobile-gap;
  }

  /* Mobile-friendly list layouts */
  .list-mobile {
    @apply space-y-3 sm:space-y-4;
  }

  .list-mobile-large {
    @apply space-y-4 sm:space-y-6;
  }

  /* Enhanced flex utilities for modern layouts */
  .flex-center {
    @apply flex items-center justify-center;
  }

  .flex-between {
    @apply flex items-center justify-between;
  }

  .flex-start {
    @apply flex items-center justify-start;
  }

  .flex-end {
    @apply flex items-center justify-end;
  }

  /* Modern section spacing */
  .section-spacing {
    @apply py-12 sm:py-16 lg:py-20;
  }

  .section-spacing-sm {
    @apply py-8 sm:py-12;
  }

  .section-spacing-lg {
    @apply py-16 sm:py-20 lg:py-24;
  }

  /* Enhanced interaction utilities */
  .hover-lift {
    @apply transition-transform duration-200 hover:scale-[1.02] hover:shadow-lg;
  }

  .hover-lift-sm {
    @apply transition-transform duration-200 hover:scale-[1.01] hover:shadow-md;
  }

  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-jobblogg-primary focus:ring-offset-2;
  }

  .focus-ring-accent {
    @apply focus:outline-none focus:ring-2 focus:ring-jobblogg-accent focus:ring-offset-2;
  }

  /* Modern card variants */
  .card-stats {
    @apply bg-white rounded-xl p-6 shadow-sm border border-jobblogg-border hover:shadow-md transition-all duration-200;
  }

  .card-interactive {
    @apply card-elevated cursor-pointer hover-lift focus-ring;
  }

  /* Layout utilities for modern design */
  .layout-stack {
    @apply space-y-6 sm:space-y-8;
  }

  .layout-stack-sm {
    @apply space-y-3 sm:space-y-4;
  }

  .layout-stack-lg {
    @apply space-y-8 sm:space-y-12;
  }

  /* Modern gradient system - Subtle and harmonious */
  .gradient-header {
    @apply bg-gradient-to-br from-jobblogg-primary to-jobblogg-accent;
  }

  .gradient-soft {
    @apply bg-gradient-to-br from-jobblogg-primary-soft to-jobblogg-accent-soft;
  }

  .gradient-blue-soft {
    @apply bg-gradient-to-br from-jobblogg-blue-50 to-jobblogg-indigo-50;
  }

  .gradient-card-hover {
    @apply bg-gradient-to-br from-jobblogg-blue-100 to-jobblogg-indigo-100;
  }

  .gradient-neutral-soft {
    @apply bg-gradient-to-br from-jobblogg-neutral to-jobblogg-neutral-light;
  }

  /* Enhanced Micro-interactions - Smooth and purposeful */
  .hover-lift {
    @apply transition-all duration-200 hover:-translate-y-0.5 hover:shadow-medium;
  }

  .hover-scale {
    @apply transition-transform duration-200 hover:scale-105;
  }

  .hover-glow {
    @apply transition-all duration-300 hover:shadow-lg hover:shadow-jobblogg-primary/20;
  }

  .hover-bounce {
    @apply transition-transform duration-200 hover:scale-110 active:scale-95;
  }

  .hover-rotate {
    @apply transition-transform duration-300 hover:rotate-3;
  }

  .hover-slide-right {
    @apply transition-transform duration-200 hover:translate-x-1;
  }

  .focus-ring-enhanced {
    @apply focus:outline-none focus:ring-2 focus:ring-jobblogg-primary focus:ring-offset-2 focus:ring-offset-white transition-all duration-200;
  }

  .interactive-press {
    @apply active:scale-95 transition-transform duration-100;
  }

  /* Mobile-specific touch interactions */
  .touch-feedback {
    @apply active:scale-95 active:bg-jobblogg-primary-soft transition-all duration-150;
  }

  .touch-ripple {
    @apply relative overflow-hidden;
  }

  .touch-ripple::before {
    content: '';
    @apply absolute inset-0 bg-current opacity-0 scale-0 rounded-full transition-all duration-300;
  }

  .touch-ripple:active::before {
    @apply opacity-10 scale-100;
  }

  /* Mobile-optimized focus states */
  .mobile-focus {
    @apply focus:outline-none focus:ring-2 focus:ring-jobblogg-primary focus:ring-offset-2 focus:ring-offset-white;
  }

  /* Swipe gesture indicators */
  .swipe-indicator {
    @apply relative;
  }

  .swipe-indicator::after {
    content: '';
    @apply absolute -right-2 top-1/2 transform -translate-y-1/2 w-1 h-8 bg-jobblogg-primary-light rounded-full opacity-30;
  }

  .focus-ring {
    @apply focus:outline-none focus:ring-2 focus:ring-jobblogg-primary focus:ring-offset-2;
  }

  /* ===== ENHANCED FORM UTILITIES ===== */
  .form-field-group {
    @apply space-y-2;
  }

  .form-field-row {
    @apply flex flex-col sm:flex-row sm:items-center gap-4;
  }

  .form-field-inline {
    @apply flex items-center gap-3;
  }

  .form-section {
    @apply space-y-6 p-6 bg-white rounded-xl border border-jobblogg-border;
  }

  .form-section-header {
    @apply pb-4 border-b border-jobblogg-border mb-6;
  }

  .form-actions {
    @apply flex flex-col sm:flex-row gap-3 pt-6 border-t border-jobblogg-border;
  }

  .form-actions-right {
    @apply flex flex-col sm:flex-row gap-3 pt-6 border-t border-jobblogg-border sm:justify-end;
  }

  .form-grid {
    @apply grid grid-cols-1 md:grid-cols-2 gap-6;
  }

  .form-grid-3 {
    @apply grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6;
  }

  /* File upload specific utilities */
  .file-drop-zone {
    @apply border-2 border-dashed rounded-xl p-8 text-center transition-all duration-200 cursor-pointer
           border-jobblogg-border hover:border-jobblogg-primary hover:bg-jobblogg-neutral;
  }

  .file-drop-zone-active {
    @apply border-jobblogg-primary bg-jobblogg-primary-soft scale-105;
  }

  .file-drop-zone-error {
    @apply border-jobblogg-error hover:border-jobblogg-error hover:bg-jobblogg-error-soft;
  }

  .file-preview-grid {
    @apply grid gap-4 grid-cols-1 sm:grid-cols-2 lg:grid-cols-3;
  }

  .file-preview-single {
    @apply grid gap-4 grid-cols-1;
  }
}

@layer utilities {
  /* Hide scrollbars while maintaining functionality */
  .scrollbar-hide {
    -ms-overflow-style: none;  /* Internet Explorer 10+ */
    scrollbar-width: none;  /* Firefox */
  }
  .scrollbar-hide::-webkit-scrollbar {
    display: none;  /* Safari and Chrome */
  }
  /* Enhanced custom animations */
  .animate-fade-in {
    animation: fadeIn 0.5s ease-in-out;
  }

  .animate-slide-up {
    animation: slideUp 0.3s ease-out;
  }

  .animate-scale-in {
    animation: scaleIn 0.2s ease-out;
  }

  .animate-slide-in-left {
    animation: slideInLeft 0.4s ease-out;
  }

  .animate-slide-in-right {
    animation: slideInRight 0.4s ease-out;
  }

  .animate-bounce-in {
    animation: bounceIn 0.6s ease-out;
  }

  .animate-pulse-soft {
    animation: pulseSoft 2s ease-in-out infinite;
  }

  .animate-shake {
    animation: shake 0.5s ease-in-out;
  }

  .animate-success-bounce {
    animation: successBounce 0.6s ease-out;
  }

  .animate-loading-dots {
    animation: loadingDots 1.4s ease-in-out infinite;
  }

  .animate-slide-out-right {
    animation: slideOutRight 0.3s ease-in;
  }
}

/* Keyframes */
@keyframes fadeIn {
  0% { opacity: 0; }
  100% { opacity: 1; }
}

@keyframes slideUp {
  0% {
    transform: translateY(10px);
    opacity: 0;
  }
  100% {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes scaleIn {
  0% {
    transform: scale(0.95);
    opacity: 0;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: .5;
  }
}

@keyframes slideInLeft {
  0% {
    transform: translateX(-20px);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideInRight {
  0% {
    transform: translateX(20px);
    opacity: 0;
  }
  100% {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes bounceIn {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes pulseSoft {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.02);
  }
}

@keyframes shake {
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-2px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(2px);
  }
}

@keyframes successBounce {
  0% {
    transform: scale(1);
  }
  25% {
    transform: scale(1.1);
  }
  50% {
    transform: scale(0.95);
  }
  75% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes loadingDots {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

@keyframes slideOutRight {
  0% {
    transform: translateX(0);
    opacity: 1;
  }
  100% {
    transform: translateX(100%);
    opacity: 0;
  }
}

/* FAB Tooltip Animation */
@keyframes fadeIn {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-fade-in {
  animation: fadeIn 0.2s ease-out;
}

/* Dialog Scale Animation */
@keyframes scaleIn {
  0% {
    opacity: 0;
    transform: scale(0.95);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-scale-in {
  animation: scaleIn 0.2s ease-out;
}

/* Scale Up Animation for Dialogs */
@keyframes scaleUp {
  from {
    opacity: 0;
    transform: scale(0.95);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

.animate-scale-up {
  animation: scaleUp 0.2s ease-out;
}
