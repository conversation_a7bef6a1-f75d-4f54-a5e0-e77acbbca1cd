import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom'
import { Authenticated, Unauthenticated, AuthLoading } from 'convex/react'
import { PWAInstallBanner } from './components/PWAInstallBanner'
import { OfflineSync, StorageUsage } from './components/OfflineSync'
import { ContractorOnboardingGuardSimple } from './components/ContractorOnboardingGuardSimple'
import TestCompanyProfile from './pages/TestCompanyProfile'
import {
  LazyDashboard,
  LazyCreateProject,
  LazyProjectLog,
  LazyProjectDetail,
  LazySignIn,
  LazySignUp,
  LazySharedProject,
  LazyConversations,
  LazyArchivedProjects,
  LazyGoogleMapsTest,
  LazyContractorOnboarding,
  LazyTeamManagement,
  LazyTeamProjects,
  LazyTeamOnboarding,
  LazyAcceptInvite,
  LazyBlockedUser,
  LazyPageWrapper,
  preloadCriticalRoutes
} from './components/LazyComponents'
import { useEffect } from 'react'

function App() {
  // Preload critical routes on app initialization
  useEffect(() => {
    preloadCriticalRoutes();
  }, []);

  return (
    <BrowserRouter>
      <div className="min-h-screen bg-white transition-colors duration-300">
        {/* PWA Components */}
        <PWAInstallBanner />
        <OfflineSync />
        <StorageUsage />
        <Routes>
          {/* Public Routes - Accessible to everyone */}
          <Route path="/shared/:sharedId" element={
            <LazyPageWrapper>
              <LazySharedProject />
            </LazyPageWrapper>
          } />
          {/* Remove chat route - now using embedded chat */}

          {/* Authentication Routes - Accessible to unauthenticated users */}
          <Route path="/sign-in" element={
            <LazyPageWrapper>
              <LazySignIn />
            </LazyPageWrapper>
          } />
          <Route path="/sign-up" element={
            <LazyPageWrapper>
              <LazySignUp />
            </LazyPageWrapper>
          } />

          {/* Team Invitation - Publicly accessible (handles auth internally) */}
          <Route path="/team-onboarding" element={
            <LazyPageWrapper>
              <LazyTeamOnboarding />
            </LazyPageWrapper>
          } />

          {/* Magic Link Accept Invitation - Publicly accessible */}
          <Route path="/accept-invite/*" element={
            <LazyPageWrapper>
              <LazyAcceptInvite />
            </LazyPageWrapper>
          } />

          {/* Contractor Onboarding - Requires authentication but bypasses onboarding guard */}
          <Route path="/contractor-onboarding/*" element={
            <>
              <Authenticated>
                <LazyPageWrapper>
                  <LazyContractorOnboarding />
                </LazyPageWrapper>
              </Authenticated>
              <Unauthenticated>
                <Navigate to="/sign-in" replace />
              </Unauthenticated>
              <AuthLoading>
                <div className="min-h-screen flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
                    <p className="text-jobblogg-text-muted">Laster autentisering...</p>
                  </div>
                </div>
              </AuthLoading>
            </>
          } />

          {/* Protected Routes - Require authentication and completed contractor onboarding */}
          <Route path="/" element={
            <>
              <Authenticated>
                <ContractorOnboardingGuardSimple>
                  <LazyPageWrapper>
                    <LazyDashboard />
                  </LazyPageWrapper>
                </ContractorOnboardingGuardSimple>
              </Authenticated>
              <Unauthenticated>
                <Navigate to="/sign-in" replace />
              </Unauthenticated>
              <AuthLoading>
                <div className="min-h-screen flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
                    <p className="text-jobblogg-text-muted">Laster autentisering...</p>
                  </div>
                </div>
              </AuthLoading>
            </>
          } />

          <Route path="/archived-projects" element={
            <>
              <Authenticated>
                <ContractorOnboardingGuardSimple>
                  <LazyPageWrapper>
                    <LazyArchivedProjects />
                  </LazyPageWrapper>
                </ContractorOnboardingGuardSimple>
              </Authenticated>
              <Unauthenticated>
                <Navigate to="/sign-in" replace />
              </Unauthenticated>
              <AuthLoading>
                <div className="min-h-screen flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
                    <p className="text-jobblogg-text-muted">Laster autentisering...</p>
                  </div>
                </div>
              </AuthLoading>
            </>
          } />
          <Route path="/conversations" element={
            <>
              <Authenticated>
                <ContractorOnboardingGuardSimple>
                  <LazyPageWrapper>
                    <LazyConversations />
                  </LazyPageWrapper>
                </ContractorOnboardingGuardSimple>
              </Authenticated>
              <Unauthenticated>
                <Navigate to="/sign-in" replace />
              </Unauthenticated>
              <AuthLoading>
                <div className="min-h-screen flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
                    <p className="text-jobblogg-text-muted">Laster autentisering...</p>
                  </div>
                </div>
              </AuthLoading>
            </>
          } />
          <Route path="/team" element={
            <>
              <Authenticated>
                <ContractorOnboardingGuardSimple>
                  <LazyPageWrapper>
                    <LazyTeamManagement />
                  </LazyPageWrapper>
                </ContractorOnboardingGuardSimple>
              </Authenticated>
              <Unauthenticated>
                <Navigate to="/sign-in" replace />
              </Unauthenticated>
              <AuthLoading>
                <div className="min-h-screen flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
                    <p className="text-jobblogg-text-muted">Laster autentisering...</p>
                  </div>
                </div>
              </AuthLoading>
            </>
          } />
          <Route path="/team/projects" element={
            <>
              <Authenticated>
                <ContractorOnboardingGuardSimple>
                  <LazyPageWrapper>
                    <LazyTeamProjects />
                  </LazyPageWrapper>
                </ContractorOnboardingGuardSimple>
              </Authenticated>
              <Unauthenticated>
                <Navigate to="/sign-in" replace />
              </Unauthenticated>
              <AuthLoading>
                <div className="min-h-screen flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
                    <p className="text-jobblogg-text-muted">Laster autentisering...</p>
                  </div>
                </div>
              </AuthLoading>
            </>
          } />

          {/* Blocked User Page - Only accessible to authenticated blocked users */}
          <Route path="/blocked" element={
            <>
              <Authenticated>
                <LazyPageWrapper>
                  <LazyBlockedUser />
                </LazyPageWrapper>
              </Authenticated>
              <Unauthenticated>
                <Navigate to="/sign-in" replace />
              </Unauthenticated>
              <AuthLoading>
                <div className="min-h-screen flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
                    <p className="text-jobblogg-text-muted">Laster autentisering...</p>
                  </div>
                </div>
              </AuthLoading>
            </>
          } />

          <Route path="/create" element={
            <>
              <Authenticated>
                <ContractorOnboardingGuardSimple>
                  <LazyPageWrapper>
                    <LazyCreateProject />
                  </LazyPageWrapper>
                </ContractorOnboardingGuardSimple>
              </Authenticated>
              <Unauthenticated>
                <Navigate to="/sign-in" replace />
              </Unauthenticated>
              <AuthLoading>
                <div className="min-h-screen flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
                    <p className="text-jobblogg-text-muted">Laster autentisering...</p>
                  </div>
                </div>
              </AuthLoading>
            </>
          } />
          <Route path="/create-wizard" element={
            <>
              <Authenticated>
                <ContractorOnboardingGuardSimple>
                  <LazyPageWrapper>
                    <LazyCreateProject />
                  </LazyPageWrapper>
                </ContractorOnboardingGuardSimple>
              </Authenticated>
              <Unauthenticated>
                <Navigate to="/sign-in" replace />
              </Unauthenticated>
              <AuthLoading>
                <div className="min-h-screen flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
                    <p className="text-jobblogg-text-muted">Laster autentisering...</p>
                  </div>
                </div>
              </AuthLoading>
            </>
          } />
          <Route path="/test-google-maps" element={
            <>
              <Authenticated>
                <ContractorOnboardingGuardSimple>
                  <LazyPageWrapper>
                    <LazyGoogleMapsTest />
                  </LazyPageWrapper>
                </ContractorOnboardingGuardSimple>
              </Authenticated>
              <Unauthenticated>
                <Navigate to="/sign-in" replace />
              </Unauthenticated>
              <AuthLoading>
                <div className="min-h-screen flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
                    <p className="text-jobblogg-text-muted">Laster autentisering...</p>
                  </div>
                </div>
              </AuthLoading>
            </>
          } />

          <Route path="/project/:projectId/details" element={
            <>
              <Authenticated>
                <ContractorOnboardingGuardSimple>
                  <LazyPageWrapper>
                    <LazyProjectDetail />
                  </LazyPageWrapper>
                </ContractorOnboardingGuardSimple>
              </Authenticated>
              <Unauthenticated>
                <Navigate to="/sign-in" replace />
              </Unauthenticated>
              <AuthLoading>
                <div className="min-h-screen flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
                    <p className="text-jobblogg-text-muted">Laster autentisering...</p>
                  </div>
                </div>
              </AuthLoading>
            </>
          } />
          <Route path="/project/:projectId" element={
            <>
              <Authenticated>
                <ContractorOnboardingGuardSimple>
                  <LazyPageWrapper>
                    <LazyProjectLog />
                  </LazyPageWrapper>
                </ContractorOnboardingGuardSimple>
              </Authenticated>
              <Unauthenticated>
                <Navigate to="/sign-in" replace />
              </Unauthenticated>
              <AuthLoading>
                <div className="min-h-screen flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
                    <p className="text-jobblogg-text-muted">Laster autentisering...</p>
                  </div>
                </div>
              </AuthLoading>
            </>
          } />
          {/* Remove chat route - now using embedded chat */}

          {/* Test Routes - Development only */}
          <Route path="/test-company-profile" element={
            <>
              <Authenticated>
                <ContractorOnboardingGuardSimple>
                  <LazyPageWrapper>
                    <TestCompanyProfile />
                  </LazyPageWrapper>
                </ContractorOnboardingGuardSimple>
              </Authenticated>
              <Unauthenticated>
                <Navigate to="/sign-in" replace />
              </Unauthenticated>
              <AuthLoading>
                <div className="min-h-screen flex items-center justify-center">
                  <div className="text-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-jobblogg-primary mx-auto mb-4"></div>
                    <p className="text-jobblogg-text-muted">Laster autentisering...</p>
                  </div>
                </div>
              </AuthLoading>
            </>
          } />
        </Routes>
      </div>
    </BrowserRouter>
  )
}

export default App
