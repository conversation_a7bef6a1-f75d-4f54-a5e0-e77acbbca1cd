import { v } from "convex/values";
import { query, mutation } from "./_generated/server";

// Enhanced project queries with team-based access control

// Get projects accessible to a user with team support
export const getAccessibleProjectsWithTeamSupport = query({
  args: {
    userId: v.string(),
    includeArchived: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    // Validate authentication
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Ikke autentisert");
    }

    if (identity.subject !== args.userId) {
      throw new Error("Ugyldig bruker-ID");
    }

    // Get the user
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.userId))
      .first();

    if (!user) {
      throw new Error("Bruker ikke funnet");
    }

    const includeArchived = args.includeArchived || false;
    let allProjects: any[] = [];

    // Get projects owned by the user
    const ownedProjects = await ctx.db
      .query("projects")
      .withIndex("by_user", (q) => q.eq("userId", args.userId))
      .filter((q) => includeArchived ? q.gte(q.field("_creationTime"), 0) : q.neq(q.field("isArchived"), true))
      .collect();

    allProjects.push(...ownedProjects);

    // Get projects assigned to the user through team assignments
    const assignments = await ctx.db
      .query("projectAssignments")
      .withIndex("by_assigned_user", (q) => q.eq("assignedUserId", args.userId))
      .filter((q) => q.eq(q.field("isActive"), true))
      .collect();

    // Fetch assigned projects
    for (const assignment of assignments) {
      const project = await ctx.db.get(assignment.projectId);
      if (project && (includeArchived || !project.isArchived)) {
        // Add assignment info to project
        const projectWithAssignment = {
          ...project,
          assignmentInfo: {
            accessLevel: assignment.accessLevel,
            assignedAt: assignment._creationTime,
            assignedBy: assignment.assignedBy,
          }
        };
        allProjects.push(projectWithAssignment);
      }
    }

    // Remove duplicates (in case user owns and is assigned to same project)
    const uniqueProjects = allProjects.filter((project, index, self) => 
      index === self.findIndex(p => p._id === project._id)
    );

    // Fetch customer data for each project
    const projectsWithCustomers = await Promise.all(
      uniqueProjects.map(async (project) => {
        const customer = await ctx.db.get(project.customerId);
        return {
          ...project,
          customer,
        };
      })
    );

    return projectsWithCustomers;
  },
});

// Get project by ID with team access validation
export const getByIdWithTeamAccess = query({
  args: {
    projectId: v.id("projects"),
    userId: v.string(),
  },
  handler: async (ctx, args) => {
    // Validate authentication
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Ikke autentisert");
    }

    if (identity.subject !== args.userId) {
      throw new Error("Ugyldig bruker-ID");
    }

    // Get the project
    const project = await ctx.db.get(args.projectId);
    if (!project) {
      return null;
    }

    // Check if user has access to this project
    const userAccess = await ctx.runQuery("teamManagement:validateUserProjectAccess" as any, {
      clerkUserId: args.userId,
      projectId: args.projectId,
    });

    if (!userAccess.hasAccess) {
      throw new Error("Ingen tilgang til dette prosjektet");
    }

    // Fetch customer data
    const customer = project.customerId ? await ctx.db.get(project.customerId) : null;

    return {
      ...project,
      customer,
      userAccessLevel: userAccess.accessLevel,
    };
  },
});

// Archive project with team access control
export const archiveProjectWithTeamAccess = mutation({
  args: {
    projectId: v.id("projects"),
    userId: v.string(),
  },
  handler: async (ctx, args) => {
    // Validate authentication
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Ikke autentisert");
    }

    if (identity.subject !== args.userId) {
      throw new Error("Ugyldig bruker-ID");
    }

    // Check if user has permission to archive this project
    const userAccess = await ctx.runQuery("teamManagement:validateUserProjectAccess" as any, {
      clerkUserId: args.userId,
      projectId: args.projectId,
    });

    if (!userAccess.hasAccess || (userAccess.accessLevel !== 'owner' && userAccess.accessLevel !== 'administrator')) {
      throw new Error("Ingen tillatelse til å arkivere dette prosjektet");
    }

    // Archive the project
    await ctx.db.patch(args.projectId, {
      isArchived: true,
      archivedAt: Date.now(),
      archivedBy: args.userId,
    });

    return { success: true };
  },
});

// Restore project with team access control
export const restoreProjectWithTeamAccess = mutation({
  args: {
    projectId: v.id("projects"),
    userId: v.string(),
  },
  handler: async (ctx, args) => {
    // Validate authentication
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Ikke autentisert");
    }

    if (identity.subject !== args.userId) {
      throw new Error("Ugyldig bruker-ID");
    }

    // Check if user has permission to restore this project
    const userAccess = await ctx.runQuery("teamManagement:validateUserProjectAccess" as any, {
      clerkUserId: args.userId,
      projectId: args.projectId,
    });

    if (!userAccess.hasAccess || (userAccess.accessLevel !== 'owner' && userAccess.accessLevel !== 'administrator')) {
      throw new Error("Ingen tillatelse til å gjenopprette dette prosjektet");
    }

    // Restore the project
    await ctx.db.patch(args.projectId, {
      isArchived: false,
      archivedAt: undefined,
      archivedBy: undefined,
    });

    return { success: true };
  },
});
