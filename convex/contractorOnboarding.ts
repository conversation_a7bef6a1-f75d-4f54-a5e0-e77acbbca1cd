import { mutation, query, action } from './_generated/server';
import { v } from 'convex/values';
import { api } from './_generated/api';

/**
 * Contractor Onboarding Functions
 * 
 * Handles the complete contractor onboarding flow including:
 * - User record management
 * - Contractor company creation
 * - Onboarding status tracking
 * - Integration with existing customer system
 */

// Get or create user record for contractor onboarding
export const getOrCreateUser = mutation({
  args: {
    clerkUserId: v.string(),
  },
  handler: async (ctx, args) => {
    // Validate authentication
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Ikke autentisert");
    }

    // Ensure the requesting user matches the Clerk user ID
    if (identity.subject !== args.clerkUserId) {
      throw new Error("Ugyldig bruker-ID");
    }

    // Check if user record already exists
    const existingUser = await ctx.db
      .query("users")
      .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.clerkUserId))
      .first();

    if (existingUser) {
      return existingUser;
    }

    // Create new user record
    const userId = await ctx.db.insert("users", {
      clerkUserId: args.clerkUserId,
      contractorCompleted: false,
      createdAt: Date.now(),
      updatedAt: Date.now(),
    });

    return await ctx.db.get(userId);
  },
});

// Check contractor onboarding status
export const getContractorOnboardingStatus = query({
  args: {
    clerkUserId: v.string(),
  },
  handler: async (ctx, args) => {
    // Validate authentication with better error handling
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      // Return a more specific error for debugging
      console.log("Authentication failed: No identity found for user", args.clerkUserId);
      throw new Error("Autentisering ikke fullført. Vennligst vent et øyeblikk og prøv igjen.");
    }

    // Ensure the requesting user matches the Clerk user ID
    if (identity.subject !== args.clerkUserId) {
      console.log("User ID mismatch:", { identity: identity.subject, requested: args.clerkUserId });
      throw new Error("Bruker-ID stemmer ikke overens. Vennligst logg inn på nytt.");
    }

    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.clerkUserId))
      .first();

    return {
      exists: !!user,
      contractorCompleted: user?.contractorCompleted || false,
      contractorCompanyId: user?.contractorCompanyId || null,
    };
  },
});

// Get contractor company by user ID
export const getContractorCompanyByUserId = query({
  args: {
    clerkUserId: v.string(),
  },
  handler: async (ctx, args) => {
    // Validate authentication with better error handling
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      console.log("Authentication failed: No identity found for user", args.clerkUserId);
      throw new Error("Autentisering ikke fullført. Vennligst vent et øyeblikk og prøv igjen.");
    }

    // Ensure the requesting user matches the Clerk user ID
    if (identity.subject !== args.clerkUserId) {
      console.log("User ID mismatch:", { identity: identity.subject, requested: args.clerkUserId });
      throw new Error("Bruker-ID stemmer ikke overens. Vennligst logg inn på nytt.");
    }

    // Find user record
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.clerkUserId))
      .first();

    if (!user || !user.contractorCompanyId) {
      return null;
    }

    // Get contractor company
    const company = await ctx.db.get(user.contractorCompanyId);
    return company;
  },
});

// Create contractor company and complete onboarding
export const createContractorCompany = mutation({
  args: {
    clerkUserId: v.string(),
    // Company data matching existing customer schema
    name: v.string(),
    contactPerson: v.string(),
    phone: v.optional(v.string()),
    email: v.optional(v.string()),
    streetAddress: v.optional(v.string()),
    postalCode: v.optional(v.string()),
    city: v.optional(v.string()),
    entrance: v.optional(v.string()),
    orgNumber: v.string(),
    notes: v.optional(v.string()),
    // Brønnøysundregisteret data tracking
    brregFetchedAt: v.optional(v.number()),
    brregData: v.optional(v.object({
      name: v.optional(v.string()),
      organizationNumber: v.optional(v.string()),
      organizationForm: v.optional(v.string()),
      organizationFormCode: v.optional(v.string()),
      naeringskode1: v.optional(v.string()),
      industryDescription: v.optional(v.string()),
      establishmentDate: v.optional(v.string()),
      numberOfEmployees: v.optional(v.number()),
      status: v.optional(v.string()),
      registryContact: v.optional(v.object({
        phone: v.optional(v.string()),
        email: v.optional(v.string()),
      })),
      visitingAddress: v.optional(v.object({
        street: v.optional(v.string()),
        postalCode: v.optional(v.string()),
        city: v.optional(v.string()),
        municipality: v.optional(v.string()),
      })),
      businessAddress: v.optional(v.object({
        street: v.optional(v.string()),
        postalCode: v.optional(v.string()),
        city: v.optional(v.string()),
        municipality: v.optional(v.string()),
      })),
      managingDirector: v.optional(v.object({
        fullName: v.optional(v.string()),
        firstName: v.optional(v.string()),
        lastName: v.optional(v.string()),
        birthDate: v.optional(v.string()),
      })),
    })),
    useCustomAddress: v.optional(v.boolean()),
  },
  handler: async (ctx, args) => {
    // Validate authentication
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Ikke autentisert");
    }

    // Ensure the requesting user matches the Clerk user ID
    if (identity.subject !== args.clerkUserId) {
      throw new Error("Ugyldig bruker-ID");
    }

    // Get or create user record
    let user = await ctx.db
      .query("users")
      .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.clerkUserId))
      .first();

    if (!user) {
      const userId = await ctx.db.insert("users", {
        clerkUserId: args.clerkUserId,
        contractorCompleted: false,
        createdAt: Date.now(),
        updatedAt: Date.now(),
      });
      user = await ctx.db.get(userId);
    }

    // Check if user already has a contractor company
    if (user!.contractorCompanyId) {
      throw new Error("Bruker har allerede registrert et entreprenørfirma");
    }

    // Validate organization number format (Norwegian format: 9 digits)
    const cleanOrgNumber = args.orgNumber.replace(/\s/g, '');
    if (!/^\d{9}$/.test(cleanOrgNumber)) {
      throw new Error("Organisasjonsnummer må være 9 siffer");
    }

    // Check if another contractor has already registered this organization number
    const existingCompany = await ctx.db
      .query("customers")
      .withIndex("by_org_number", (q) => q.eq("orgNumber", cleanOrgNumber))
      .filter((q) => q.neq(q.field("contractorUserId"), null))
      .filter((q) => q.neq(q.field("contractorUserId"), args.clerkUserId))
      .first();

    if (existingCompany) {
      throw new Error("Denne bedriften er allerede registrert i JobbLogg. Kontakt support hvis du mener dette er en feil.");
    }

    // Create contractor company record
    const companyId = await ctx.db.insert("customers", {
      name: args.name.trim(),
      type: "bedrift" as const,
      contactPerson: args.contactPerson.trim(),
      phone: args.phone?.trim() || undefined,
      email: args.email?.trim() || undefined,
      streetAddress: args.streetAddress?.trim() || undefined,
      postalCode: args.postalCode?.trim() || undefined,
      city: args.city?.trim() || undefined,
      entrance: args.entrance?.trim() || undefined,
      orgNumber: cleanOrgNumber,
      notes: args.notes?.trim() || undefined,
      // Mark as contractor company
      contractorUserId: args.clerkUserId,
      // Brønnøysundregisteret data tracking
      brregFetchedAt: args.brregFetchedAt || undefined,
      brregData: args.brregData || undefined,
      useCustomAddress: args.useCustomAddress || undefined,
      // Use contractor's Clerk ID as userId for consistency
      userId: args.clerkUserId,
      createdAt: Date.now(),
    });

    // Update user record with contractor company reference, completion status, and administrator role
    await ctx.db.patch(user!._id, {
      contractorCompanyId: companyId,
      contractorCompleted: true,
      role: "administrator", // Set as administrator since they created the company
      updatedAt: Date.now(),
    });

    console.log('✅ Contractor onboarding completed:', {
      clerkUserId: args.clerkUserId,
      companyId,
      role: 'administrator',
      companyName: args.name
    });

    // Return the created company
    return await ctx.db.get(companyId);
  },
});

// Validate organization number for contractor (allows same orgNumber for different users)
export const validateOrgNumberForContractor = query({
  args: {
    orgNumber: v.string(),
    clerkUserId: v.string(),
  },
  handler: async (ctx, args) => {
    // Validate authentication
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Ikke autentisert");
    }

    // Ensure the requesting user matches the Clerk user ID
    if (identity.subject !== args.clerkUserId) {
      throw new Error("Ugyldig bruker-ID");
    }

    const cleanOrgNumber = args.orgNumber.replace(/\s/g, '');

    // Basic format validation
    if (!/^\d{9}$/.test(cleanOrgNumber)) {
      return {
        isValid: false,
        error: "Organisasjonsnummer må være 9 siffer",
      };
    }

    // Check if this user already has a contractor company with different org number
    const existingContractorCompany = await ctx.db
      .query("customers")
      .withIndex("by_contractor_user", (q) => q.eq("contractorUserId", args.clerkUserId))
      .first();

    if (existingContractorCompany && existingContractorCompany.orgNumber !== cleanOrgNumber) {
      return {
        isValid: false,
        error: "Du har allerede registrert et annet organisasjonsnummer",
      };
    }

    // Check if another contractor has already registered this org number
    const existingCompany = await ctx.db
      .query("customers")
      .withIndex("by_org_number", (q) => q.eq("orgNumber", cleanOrgNumber))
      .filter((q) => q.neq(q.field("contractorUserId"), null))
      .filter((q) => q.neq(q.field("contractorUserId"), args.clerkUserId))
      .first();

    if (existingCompany) {
      return {
        isValid: false,
        error: "Dette organisasjonsnummeret er allerede registrert av en annen entreprenør",
        isAlreadyRegistered: true,
      };
    }

    return {
      isValid: true,
      error: null,
    };
  },
});

// Update contractor onboarding status (for manual completion or reset)
export const updateContractorOnboardingStatus = mutation({
  args: {
    clerkUserId: v.string(),
    completed: v.boolean(),
  },
  handler: async (ctx, args) => {
    // Validate authentication
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Ikke autentisert");
    }

    // Ensure the requesting user matches the Clerk user ID
    if (identity.subject !== args.clerkUserId) {
      throw new Error("Ugyldig bruker-ID");
    }

    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.clerkUserId))
      .first();

    if (!user) {
      throw new Error("Bruker ikke funnet");
    }

    await ctx.db.patch(user._id, {
      contractorCompleted: args.completed,
      updatedAt: Date.now(),
    });

    return { success: true };
  },
});

/**
 * Fix missing administrator role for existing contractor company owners
 * This function sets administrator role for users who created companies but don't have the role
 */
export const fixMissingAdministratorRole = mutation({
  args: {
    clerkUserId: v.string(),
  },
  handler: async (ctx, args) => {
    // Validate authentication
    const identity = await ctx.auth.getUserIdentity();
    if (!identity) {
      throw new Error("Ikke autentisert");
    }

    // Ensure the requesting user matches the Clerk user ID
    if (identity.subject !== args.clerkUserId) {
      throw new Error("Ugyldig bruker-ID");
    }

    // Find user record
    const user = await ctx.db
      .query("users")
      .withIndex("by_clerk_user_id", (q) => q.eq("clerkUserId", args.clerkUserId))
      .first();

    if (!user) {
      throw new Error("Bruker ikke funnet");
    }

    if (!user.contractorCompanyId) {
      throw new Error("Bruker har ikke en contractor company");
    }

    // Get the company to verify ownership
    const company = await ctx.db.get(user.contractorCompanyId);
    if (!company) {
      throw new Error("Contractor company ikke funnet");
    }

    // Verify this user is the owner of the company
    if (company.contractorUserId !== args.clerkUserId) {
      throw new Error("Du er ikke eier av denne bedriften");
    }

    // Check if user already has administrator role
    if (user.role === "administrator") {
      return {
        success: true,
        message: "Bruker har allerede administrator rolle",
        alreadyAdmin: true
      };
    }

    // Set administrator role
    await ctx.db.patch(user._id, {
      role: "administrator",
      updatedAt: Date.now(),
    });

    console.log('✅ Administrator rolle satt for eksisterende contractor:', {
      clerkUserId: args.clerkUserId,
      companyName: company.name,
      previousRole: user.role || 'ingen rolle'
    });

    return {
      success: true,
      message: "Administrator rolle satt",
      alreadyAdmin: false
    };
  },
});

// Action wrapper for organization number validation (for use in frontend)
export const validateOrgNumberAction = action({
  args: {
    orgNumber: v.string(),
    clerkUserId: v.string(),
  },
  handler: async (ctx, args): Promise<{ isValid: boolean; error: string | null; isAlreadyRegistered?: boolean }> => {
    // Call the query function directly
    return await ctx.runQuery(api.contractorOnboarding.validateOrgNumberForContractor, {
      orgNumber: args.orgNumber,
      clerkUserId: args.clerkUserId,
    });
  },
});
