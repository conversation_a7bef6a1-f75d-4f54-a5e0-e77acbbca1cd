import { action } from './_generated/server';
import { v } from 'convex/values';
import { Resend } from 'resend';
import { generateTeamInvitationEmail, generateMagicLinkInvitationEmail } from './emailTemplates';

// Send team invitation email
export const sendTeamInvitationEmail = action({
  args: {
    to: v.string(),
    invitedByName: v.string(),
    inviterEmail: v.string(),
    companyName: v.string(),
    invitationLink: v.string(),
    expiresAt: v.number(),
    role: v.union(v.literal('administrator'), v.literal('utfoerende')),
  },
  handler: async (ctx, args) => {
    try {
      console.log('📧 Sending team invitation email to:', args.to);

      // Initialize Resend with API key
      const apiKey = 're_Djk6vWC8_ChYiFGm8F7XbAkJi9ceWEBYL'; // Your actual API key
      const resend = new Resend(apiKey);

      // Generate email content using template
      const emailContent = generateTeamInvitationEmail({
        invitedByName: args.invitedByName,
        inviterEmail: args.inviterEmail,
        companyName: args.companyName,
        invitationLink: args.invitationLink,
        expiresAt: args.expiresAt,
        role: args.role,
      });

      // Send email via Resend
      const result = await resend.emails.send({
        from: '<EMAIL>', // Use Resend's default verified domain for testing
        to: args.to,
        subject: emailContent.subject,
        html: emailContent.html,
        text: emailContent.text,
        // Temporarily remove tags to fix validation error
        // tags: [
        //   { name: 'type', value: 'team_invitation' },
        //   { name: 'role', value: args.role },
        // ],
      });

      console.log('✅ Email sent successfully:', result);

      console.log('✅ Email sent successfully:', result);

      return {
        success: true,
        emailId: result.data?.id,
        message: 'Invitasjons-email sendt',
      };

    } catch (error) {
      console.error('❌ Failed to send email:', error);

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Ukjent feil ved sending av e-post',
        message: 'Kunne ikke sende invitasjons-email',
      };
    }
  },
});

// Send magic link team invitation email
export const sendMagicLinkInvitationEmail = action({
  args: {
    to: v.string(),
    firstName: v.string(),
    lastName: v.string(),
    invitedByName: v.string(),
    inviterEmail: v.string(),
    companyName: v.string(),
    magicLink: v.string(),
    expiresAt: v.number(),
    role: v.union(v.literal('administrator'), v.literal('utfoerende')),
  },
  handler: async (ctx, args) => {
    try {
      console.log('📧 Sending magic link invitation email to:', args.to);

      // Initialize Resend with API key
      const apiKey = 're_Djk6vWC8_ChYiFGm8F7XbAkJi9ceWEBYL'; // Your actual API key

      if (!apiKey || apiKey.length < 10) {
        throw new Error('Resend API key not configured');
      }

      const resend = new Resend(apiKey);
      console.log('🔑 Resend initialized with API key:', apiKey.substring(0, 10) + '...');

      // Generate email content using template
      const emailContent = generateMagicLinkInvitationEmail({
        firstName: args.firstName,
        lastName: args.lastName,
        invitedByName: args.invitedByName,
        inviterEmail: args.inviterEmail,
        companyName: args.companyName,
        magicLink: args.magicLink,
        expiresAt: args.expiresAt,
        role: args.role,
      });

      // In development/testing mode, Resend can only send to verified email addresses
      // For testing purposes, we'll send to the verified email but include original recipient info
      const isDevelopment = true; // Set to false when you have a verified domain
      const verifiedEmail = '<EMAIL>'; // Your verified email address

      const actualRecipient = args.to;
      const emailRecipient = isDevelopment ? verifiedEmail : actualRecipient;

      // Modify subject and content for development mode
      let modifiedSubject = emailContent.subject;
      let modifiedHtml = emailContent.html;
      let modifiedText = emailContent.text;

      if (isDevelopment && actualRecipient !== verifiedEmail) {
        modifiedSubject = `[DEV - For: ${actualRecipient}] ${emailContent.subject}`;

        const devNotice = `
          <div style="background: #fef3c7; border: 1px solid #fbbf24; padding: 15px; margin: 20px 0; border-radius: 8px;">
            <strong>🧪 DEVELOPMENT MODE</strong><br>
            This email was intended for: <strong>${actualRecipient}</strong><br>
            But sent to your verified email for testing purposes.
          </div>
        `;

        modifiedHtml = modifiedHtml.replace('<div class="content">', `<div class="content">${devNotice}`);
        modifiedText = `[DEVELOPMENT MODE - Intended for: ${actualRecipient}]\n\n${modifiedText}`;
      }

      // Send email via Resend
      const emailPayload = {
        from: '<EMAIL>', // Use Resend's default verified domain for testing
        to: emailRecipient,
        subject: modifiedSubject,
        html: modifiedHtml,
        text: modifiedText,
        // Temporarily remove tags to fix validation error
        // tags: [
        //   { name: 'type', value: 'magic_link_invitation' },
        //   { name: 'role', value: args.role },
        //   { name: 'mode', value: isDevelopment ? 'development' : 'production' },
        //   { name: 'recipient_type', value: isDevelopment ? 'dev_redirect' : 'direct' },
        // ],
      };

      console.log('📤 Sending email with payload:', {
        from: emailPayload.from,
        to: emailPayload.to,
        originalRecipient: actualRecipient,
        subject: emailPayload.subject,
        role: args.role,
        isDevelopment,
        htmlLength: emailPayload.html.length,
        textLength: emailPayload.text.length
      });

      const result = await resend.emails.send(emailPayload);

      console.log('✅ Magic link email sent successfully:', {
        success: !!result.data,
        emailId: result.data?.id,
        error: result.error
      });

      const successMessage = isDevelopment && actualRecipient !== verifiedEmail
        ? `Magic link invitasjon sendt til ${verifiedEmail} (DEV MODE - ment for ${args.firstName} ${args.lastName} - ${actualRecipient})`
        : `Magic link invitasjon sendt til ${args.firstName} ${args.lastName} (${args.to})`;

      return {
        success: true,
        emailId: result.data?.id,
        message: successMessage,
        developmentMode: isDevelopment,
        actualRecipient: emailRecipient,
        intendedRecipient: actualRecipient,
      };

    } catch (error) {
      console.error('❌ Failed to send magic link email:', {
        error: error instanceof Error ? error.message : error,
        stack: error instanceof Error ? error.stack : undefined,
        to: args.to,
        role: args.role,
        companyName: args.companyName
      });

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Ukjent feil ved sending av e-post',
        message: 'Kunne ikke sende magic link invitasjons-email',
      };
    }
  },
});

// Test Resend API connection
export const testResendConnection = action({
  args: {
    testEmail: v.string(),
  },
  handler: async (ctx, args) => {
    try {
      console.log('🧪 Testing Resend API connection...');

      // Initialize Resend with API key
      const apiKey = 're_Djk6vWC8_ChYiFGm8F7XbAkJi9ceWEBYL';

      if (!apiKey || apiKey.length < 10) {
        throw new Error('Resend API key not configured');
      }

      const resend = new Resend(apiKey);

      // Send simple test email
      const result = await resend.emails.send({
        from: '<EMAIL>',
        to: args.testEmail,
        subject: 'Test Email from JobbLogg',
        html: '<h1>Test Email</h1><p>This is a test email to verify Resend integration is working.</p>',
        text: 'Test Email - This is a test email to verify Resend integration is working.',
      });

      console.log('✅ Test email result:', result);

      return {
        success: !!result.data,
        emailId: result.data?.id,
        error: result.error,
        message: result.data ? 'Test email sent successfully' : 'Failed to send test email'
      };

    } catch (error) {
      console.error('❌ Resend test failed:', error);

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        message: 'Resend API test failed'
      };
    }
  },
});

// Send general notification email (for future use)
export const sendNotificationEmail = action({
  args: {
    to: v.string(),
    subject: v.string(),
    html: v.string(),
    text: v.optional(v.string()),
    tags: v.optional(v.array(v.object({
      name: v.string(),
      value: v.string(),
    }))),
  },
  handler: async (ctx, args) => {
    try {
      console.log('📧 Sending notification email to:', args.to);

      // Initialize Resend with API key
      const apiKey = 're_Djk6vWC8_ChYiFGm8F7XbAkJi9ceWEBYL'; // Your actual API key
      const resend = new Resend(apiKey);

      const result = await resend.emails.send({
        from: '<EMAIL>', // Use Resend's default verified domain for testing
        to: args.to,
        subject: args.subject,
        html: args.html,
        text: args.text || args.html.replace(/<[^>]*>/g, ''), // Strip HTML for text version
        tags: args.tags || [],
      });

      console.log('✅ Notification email sent:', result);

      console.log('✅ Notification email sent:', result);

      return {
        success: true,
        emailId: result.data?.id,
        message: 'E-post sendt',
      };

    } catch (error) {
      console.error('❌ Failed to send notification email:', error);

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Ukjent feil ved sending av e-post',
        message: 'Kunne ikke sende e-post',
      };
    }
  },
});

// Test email function for development
export const sendTestEmail = action({
  args: {
    to: v.string(),
  },
  handler: async (ctx, args) => {
    try {
      console.log('📧 Sending test email to:', args.to);

      // Initialize Resend with API key
      const apiKey = 're_Djk6vWC8_ChYiFGm8F7XbAkJi9ceWEBYL'; // Your actual API key
      console.log('📧 Using API key:', apiKey.substring(0, 10) + '...');
      const resend = new Resend(apiKey);
      console.log('📧 Resend client initialized');

      console.log('📧 About to send email...');
      const emailData = {
        from: '<EMAIL>', // Use Resend's default verified domain for testing
        to: args.to,
        subject: 'Test email fra JobbLogg',
        html: `
          <h1>Test Email</h1>
          <p>Dette er en test-email fra JobbLogg for å verifisere at email-sending fungerer.</p>
          <p>Sendt: ${new Date().toLocaleString('nb-NO')}</p>
        `,
        text: `Test Email\n\nDette er en test-email fra JobbLogg for å verifisere at email-sending fungerer.\n\nSendt: ${new Date().toLocaleString('nb-NO')}`,
      };
      console.log('📧 Email data:', JSON.stringify(emailData, null, 2));

      const result = await resend.emails.send(emailData);

      console.log('✅ Email sent successfully:', result);
      console.log('📧 Result data:', JSON.stringify(result, null, 2));

      return {
        success: true,
        emailId: result.data?.id,
        message: 'Test-email sendt',
        debug: result,
      };

    } catch (error) {
      console.error('❌ Test email failed:', error);
      console.error('❌ Error details:', JSON.stringify(error, null, 2));

      return {
        success: false,
        error: error instanceof Error ? error.message : 'Ukjent feil',
        message: 'Test-email feilet',
        debug: error,
      };
    }
  },
});
