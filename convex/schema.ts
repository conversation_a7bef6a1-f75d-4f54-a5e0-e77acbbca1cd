import { defineSchema, defineTable } from 'convex/server';
import { v } from 'convex/values';

// TypeScript interfaces for better type safety and documentation
export interface ShareSettings {
  showContractorNotes: boolean;        // Show contractor-specific notes to customers
  accessCount: number;                 // Track how many times project was accessed - milliseconds since Unix epoch
  lastAccessedAt?: number;             // Track last access time - milliseconds since Unix epoch
}

export interface JobPhoto {
  url: string;                         // Image URL from Convex storage
  note?: string;                       // Optional comment/note for the image
  capturedAt?: number;                 // Timestamp when photo was taken - milliseconds since Unix epoch
}

export interface JobData {
  jobDescription: string;              // "Hva skal gjøres?" - detailed job description
  photos: JobPhoto[];                  // "Bilder fra befaring" - site inspection photos
  accessNotes: string;                 // "Tilkomst og forhold" - access and site conditions
  equipmentNeeds: string;              // "Hva må medbringes?" - equipment and materials needed
  unresolvedQuestions: string;         // "Hva må avklares?" - questions that need clarification
  personalNotes: string;               // "Egne notater" - contractor's personal notes
}

export interface MessageFile {
  url: string;                         // Signed URL to file storage
  name: string;                        // Original filename
  size: number;                        // File size in bytes
  type: string;                        // MIME type
  thumbnailUrl?: string;               // Thumbnail URL for images/videos
}

export interface MessageReaction {
  emoji: string;                       // Emoji character (e.g., "👍", "❤️")
  userIds: string[];                   // Array of user IDs who reacted with this emoji
  count: number;                       // Count of reactions (for performance)
}

export type DeliveryStatus = "sending" | "sent" | "delivered" | "failed";
export type CustomerType = "privat" | "bedrift";
export type SenderRole = "customer" | "contractor";
export type EntryType = "user" | "system";

// Custom validators for enhanced data integrity
const positiveNumber = v.number(); // TODO: Add custom validator for positive numbers
const timestampValidator = v.number(); // milliseconds since Unix epoch

export default defineSchema({
  // User data table for contractor onboarding and team management
  users: defineTable({
    clerkUserId: v.string(),             // Clerk user ID (unique identifier from authentication)
    contractorCompleted: v.optional(v.boolean()), // Whether contractor onboarding is completed
    contractorCompanyId: v.optional(v.id("customers")), // Reference to contractor's company record

    // Multi-user team management fields
    role: v.optional(v.union(v.literal("administrator"), v.literal("utfoerende"))), // User role within company
    invitedBy: v.optional(v.string()),   // Clerk ID of administrator who invited this user
    invitationToken: v.optional(v.string()), // Unique token for invitation process
    invitationStatus: v.optional(v.union(
      v.literal("pending"),              // Invitation sent but not accepted
      v.literal("accepted"),             // Invitation accepted and user onboarded
      v.literal("expired")               // Invitation expired or cancelled
    )),
    invitedAt: v.optional(timestampValidator), // When invitation was sent - milliseconds since Unix epoch
    acceptedAt: v.optional(timestampValidator), // When invitation was accepted - milliseconds since Unix epoch

    // Magic link invitation data (pre-filled for registration)
    invitationEmail: v.optional(v.string()), // Email from invitation
    invitationFirstName: v.optional(v.string()), // First name from invitation
    invitationLastName: v.optional(v.string()), // Last name from invitation
    invitationPhone: v.optional(v.string()), // Phone from invitation

    // Team member lifecycle management
    isActive: v.optional(v.boolean()),   // Whether user is active in the team (default: true)
    deletedAt: v.optional(timestampValidator), // When user was removed from team - milliseconds since Unix epoch
    deletedBy: v.optional(v.string()),   // Clerk ID of administrator who removed this user

    // Activity tracking for "sist logget inn" functionality
    lastLoginAt: v.optional(timestampValidator), // Last login timestamp - milliseconds since Unix epoch
    lastActivityAt: v.optional(timestampValidator), // Last activity timestamp - milliseconds since Unix epoch

    // User blocking/suspension functionality
    isBlocked: v.optional(v.boolean()),  // Whether user is blocked from team access
    blockedAt: v.optional(timestampValidator), // When user was blocked - milliseconds since Unix epoch
    blockedBy: v.optional(v.string()),   // Clerk ID of administrator who blocked this user
    blockedReason: v.optional(v.string()), // Optional reason for blocking (for audit trail)

    createdAt: timestampValidator,       // Account creation timestamp - milliseconds since Unix epoch
    updatedAt: v.optional(timestampValidator), // Last update timestamp - milliseconds since Unix epoch
  })
    .index("by_clerk_user_id", ["clerkUserId"])
    .index("by_contractor_company", ["contractorCompanyId"])
    .index("by_onboarding_status", ["contractorCompleted"])
    .index("by_invitation_token", ["invitationToken"])
    .index("by_company_and_role", ["contractorCompanyId", "role"])
    .index("by_invited_by", ["invitedBy"])
    .index("by_invitation_status", ["invitationStatus"])
    .index("by_active_status", ["isActive"])
    .index("by_last_activity", ["lastActivityAt"])
    .index("by_blocked_status", ["isBlocked"]),

  // Customer data table for AI-agent friendly structure
  customers: defineTable({
    name: v.string(),                    // Customer name (required) - shown in UI and search
    type: v.union(v.literal("privat"), v.literal("bedrift")), // Customer type: simplified to two types
    contactPerson: v.optional(v.string()), // Contact person (if type === "bedrift") - e.g. "Arne Løken"
    phone: v.optional(v.string()),       // Phone number (optional) - for quick contact
    email: v.optional(v.string()),       // Email address (optional) - used in reporting or notifications
    // Enhanced address structure
    address: v.optional(v.string()),     // Legacy single address field (for backward compatibility)
    streetAddress: v.optional(v.string()), // Street address (required for new customers) - e.g. "Storgata 15"
    postalCode: v.optional(v.string()),  // Postal code (required for new customers) - e.g. "0123"
    city: v.optional(v.string()),        // City/Town (required for new customers) - e.g. "Oslo"
    entrance: v.optional(v.string()),    // Entrance/Floor info (optional) - e.g. "Oppgang A, 2. etasje"
    orgNumber: v.optional(v.string()),   // Organization number (optional, only for bedrift) - legal ID
    notes: v.optional(v.string()),       // Notes (optional) - free text: key code, "customer is allergic to dogs", etc.
    // Contractor company identification
    contractorUserId: v.optional(v.string()), // Clerk user ID if this is a contractor's company record
    // Brønnøysundregisteret data tracking
    brregFetchedAt: v.optional(timestampValidator), // Timestamp when data was fetched from Brønnøysundregisteret - milliseconds since Unix epoch
    brregData: v.optional(v.object({     // Original Brønnøysundregisteret data (for reference and freshness)
      name: v.optional(v.string()),      // Company name from Brreg
      orgNumber: v.optional(v.string()), // Organization number from Brreg
      organizationNumber: v.optional(v.string()), // Alternative field name for organization number
      status: v.optional(v.string()),    // Company status (active, inactive, etc.)
      industryCode: v.optional(v.string()), // Industry code (NACE code) - legacy field
      industryDescription: v.optional(v.string()), // Industry description
      // Enhanced Brønnøysundregisteret fields
      organizationForm: v.optional(v.string()), // Organization form description (e.g., "Aksjeselskap")
      organizationFormCode: v.optional(v.string()), // Organization form code (e.g., "AS")
      naeringskode1: v.optional(v.string()), // Primary NACE industry code (5-digit)
      establishmentDate: v.optional(v.string()), // Company founding date (ISO format)
      numberOfEmployees: v.optional(v.number()), // Number of employees
      registryContact: v.optional(v.object({ // Contact information from registry
        phone: v.optional(v.string()),   // Phone number from registry
        email: v.optional(v.string())    // Email from registry
      })),
      managingDirector: v.optional(v.union(
        v.string(), // Legacy format: just the name as string
        v.object({  // New detailed format from Brønnøysundregisteret
          birthDate: v.optional(v.string()),
          firstName: v.optional(v.string()),
          fullName: v.optional(v.string()),
          lastName: v.optional(v.string())
        })
      )), // Managing director from Brreg
      businessAddress: v.optional(v.object({ // Business address from Brreg
        street: v.optional(v.string()),
        postalCode: v.optional(v.string()),
        city: v.optional(v.string()),
        municipality: v.optional(v.string())
      })),
      visitingAddress: v.optional(v.object({ // Visiting address from Brreg (if different)
        street: v.optional(v.string()),
        postalCode: v.optional(v.string()),
        city: v.optional(v.string()),
        municipality: v.optional(v.string())
      }))
    })),
    // Address override tracking
    useCustomAddress: v.optional(v.boolean()), // Whether user chose to override Brreg address
    userId: v.string(),                  // Owner of this customer record
    createdAt: timestampValidator        // Creation timestamp - milliseconds since Unix epoch
  })
    .index("by_user", ["userId"])
    .index("by_type", ["type"])
    .index("by_user_and_type", ["userId", "type"])
    .index("by_org_number", ["orgNumber"])
    .index("by_contractor_user", ["contractorUserId"]),

  projects: defineTable({
    name: v.string(),
    description: v.string(),
    userId: v.string(),
    customerId: v.optional(v.id("customers")), // Reference to customer - enables AI queries like "projects for customer X"
    sharedId: v.string(),
    createdAt: timestampValidator,         // Creation timestamp - milliseconds since Unix epoch
    // Archive management - preserves data while removing from active view
    isArchived: v.optional(v.boolean()),       // Archive status - defaults to false for active projects
    archivedAt: v.optional(timestampValidator), // Timestamp when project was archived - milliseconds since Unix epoch
    archivedBy: v.optional(v.string()),        // User ID who archived the project
    // Project sharing configuration
    isPubliclyShared: v.optional(v.boolean()), // Enable/disable public sharing
    shareSettings: v.optional(v.object({
      showContractorNotes: v.boolean(),        // Show contractor-specific notes to customers
      accessCount: positiveNumber,             // Track how many times project was accessed
      lastAccessedAt: v.optional(timestampValidator) // Track last access time - milliseconds since Unix epoch
    })),
    // Job information for contractor workflow documentation
    jobData: v.optional(v.object({
      jobDescription: v.string(),           // "Hva skal gjøres?" - detailed job description
      photos: v.array(v.object({           // "Bilder fra befaring" - site inspection photos
        url: v.string(),                   // Image URL from Convex storage
        note: v.optional(v.string()),      // Optional comment/note for the image
        capturedAt: v.optional(timestampValidator) // Timestamp when photo was taken - milliseconds since Unix epoch
      })),
      accessNotes: v.string(),             // "Tilkomst og forhold" - access and site conditions
      equipmentNeeds: v.string(),          // "Hva må medbringes?" - equipment and materials needed
      unresolvedQuestions: v.string(),     // "Hva må avklares?" - questions that need clarification
      personalNotes: v.string()            // "Egne notater" - contractor's personal notes
    }))
  })
    .index("by_user", ["userId"])
    .index("by_shared_id", ["sharedId"])
    .index("by_customer", ["customerId"])
    .index("by_user_and_customer", ["userId", "customerId"])
    .index("by_user_and_archive_status", ["userId", "isArchived"])  // Efficient querying of active vs archived projects
    .index("by_archived_status", ["isArchived"]),                   // Global archive status queries

  logEntries: defineTable({
    projectId: v.id("projects"),
    userId: v.string(),
    description: v.string(),
    imageId: v.optional(v.id("_storage")),
    createdAt: timestampValidator,         // Creation timestamp - milliseconds since Unix epoch
    // Entry type to distinguish between user content and system activities
    entryType: v.optional(v.union(v.literal("user"), v.literal("system"))), // "user" for regular entries, "system" for archive/restore activities
    // Edit history and tracking fields
    isEdited: v.optional(v.boolean()),
    lastEditedAt: v.optional(timestampValidator), // Last edit timestamp - milliseconds since Unix epoch
    editHistory: v.optional(v.array(v.object({
      version: positiveNumber,
      editedAt: timestampValidator,       // Edit timestamp - milliseconds since Unix epoch
      description: v.string(),
      imageId: v.optional(v.id("_storage")),
      changeType: v.union(v.literal("description"), v.literal("image"), v.literal("both")), // Enum instead of free string
      changeSummary: v.string()
    })))
  })
    .index("by_project", ["projectId"])
    .index("by_user", ["userId"])
    .index("by_project_and_user", ["projectId", "userId"]),



  // Customer image likes - customers can "like" images in shared projects
  imageLikes: defineTable({
    logEntryId: v.id("logEntries"),           // Reference to the log entry with the image
    projectId: v.id("projects"),              // Reference to project (for easier querying)
    sharedId: v.string(),                     // For validation that like came from shared link

    // Customer identification (anonymous but consistent within session)
    customerSessionId: v.string(),            // Unique session identifier (nanoid) for anonymous customer
    customerName: v.optional(v.string()),     // Optional name if customer provided it
    customerEmail: v.optional(v.string()),    // Optional email if customer provided it

    // Like metadata
    createdAt: timestampValidator,            // Creation timestamp - milliseconds since Unix epoch
    ipAddress: v.optional(v.string()),        // For spam prevention
  })
    .index("by_log_entry", ["logEntryId"])
    .index("by_project", ["projectId"])
    .index("by_shared_id", ["sharedId"])
    .index("by_customer_session", ["customerSessionId"])
    .index("by_log_entry_and_customer", ["logEntryId", "customerSessionId"])
    .index("by_project_and_customer", ["projectId", "customerSessionId"]),

  // Chat messages - threaded conversations on log entries
  messages: defineTable({
    logId: v.id("logEntries"),                // Always linked to a log entry (Prosjekt → Logg → Tråd)
    parentId: v.optional(v.id("messages")),   // null = root message (log description), value = reply to message

    // Sender information
    senderId: v.string(),                     // User ID of message sender
    senderRole: v.union(v.literal("customer"), v.literal("contractor")), // Role for display name mapping

    // Message content - at least one of text or file must be provided
    text: v.optional(v.string()),             // Message text content (required if no file)
    file: v.optional(v.object({               // File attachment metadata (required if no text)
      url: v.string(),                        // Signed URL to file storage
      name: v.string(),                       // Original filename
      size: positiveNumber,                   // File size in bytes
      type: v.string(),                       // MIME type
      thumbnailUrl: v.optional(v.string())    // Thumbnail URL for images/videos
    })),

    // Reactions and engagement
    reactions: v.optional(v.array(v.object({
      emoji: v.string(),                      // Emoji character (e.g., "👍", "❤️")
      userIds: v.array(v.string()),           // Array of user IDs who reacted with this emoji
      count: positiveNumber                   // Count of reactions (for performance)
    }))),

    // Read status tracking
    readBy: v.optional(v.record(v.string(), timestampValidator)), // Dynamic object: { [userId]: timestamp }

    // Delivery status tracking
    deliveryStatus: v.optional(v.union(
      v.literal("sending"),     // Message is being sent
      v.literal("sent"),        // Message sent to server
      v.literal("delivered"),   // Message delivered to recipient(s)
      v.literal("failed")       // Message failed to send
    )),
    deliveredTo: v.optional(v.record(v.string(), timestampValidator)), // { [userId]: timestamp } when delivered
    failureReason: v.optional(v.string()), // Error message if delivery failed

    // Message metadata
    createdAt: timestampValidator,            // Creation timestamp - milliseconds since Unix epoch
    updatedAt: v.optional(timestampValidator), // Last edit timestamp - milliseconds since Unix epoch
    isEdited: v.optional(v.boolean()),        // Whether message has been edited
    isDeleted: v.optional(v.boolean())        // Soft delete flag
  })
    // Custom validation: at least one of text or file must be provided
    // This will be enforced in the mutation handlers since Convex doesn't support cross-field validation
    .index("by_log", ["logId"])               // Get all messages for a log entry
    .index("by_parent", ["parentId"])         // Get replies to a message
    .index("by_sender", ["senderId"])         // Get messages by sender
    .index("by_created", ["createdAt"])       // Chronological ordering
    .index("by_log_and_created", ["logId", "createdAt"]) // Efficient log message ordering
    .index("by_log_and_parent", ["logId", "parentId"]),   // Thread structure queries

  // Typing indicators for real-time chat (updated for proper deployment)
  typingIndicators: defineTable({
    logId: v.id("logEntries"),                // Log entry where user is typing
    userId: v.string(),                       // User who is typing
    userRole: v.union(v.literal("customer"), v.literal("contractor")), // Role for display
    expiresAt: timestampValidator,            // Timestamp when indicator expires - milliseconds since Unix epoch
    createdAt: timestampValidator,            // When typing started - milliseconds since Unix epoch
    updatedAt: timestampValidator             // Last activity timestamp - milliseconds since Unix epoch
  })
    .index("by_log", ["logId"])               // Get all typing indicators for a log
    .index("by_log_and_user", ["logId", "userId"]) // Get specific user's typing indicator
    .index("by_expires", ["expiresAt"]),      // For cleanup of expired indicators

  // Project assignments for team collaboration
  projectAssignments: defineTable({
    projectId: v.id("projects"),              // Reference to assigned project
    assignedUserId: v.string(),               // Clerk ID of user assigned to project
    assignedBy: v.string(),                   // Clerk ID of administrator who made assignment
    assignedAt: timestampValidator,           // When assignment was made - milliseconds since Unix epoch
    accessLevel: v.union(
      v.literal("owner"),                     // Full project ownership (can edit, delete, assign)
      v.literal("collaborator"),              // Can edit project and add log entries
      v.literal("viewer")                     // Read-only access to project
    ),
    isActive: v.optional(v.boolean()),        // Whether assignment is currently active (defaults to true)
    revokedAt: v.optional(timestampValidator), // When access was revoked - milliseconds since Unix epoch
    revokedBy: v.optional(v.string()),        // Clerk ID of administrator who revoked access
    notes: v.optional(v.string())             // Optional notes about the assignment
  })
    .index("by_project", ["projectId"])                           // Get all assignments for a project
    .index("by_assigned_user", ["assignedUserId"])                // Get all assignments for a user
    .index("by_project_and_user", ["projectId", "assignedUserId"]) // Check specific user's access to project
    .index("by_assigned_by", ["assignedBy"])                      // Track assignments made by administrator
    .index("by_active_status", ["isActive"])                      // Filter active/inactive assignments
    .index("by_access_level", ["accessLevel"]),                  // Query by access level

  // Link previews for OpenGraph metadata caching
  linkPreviews: defineTable({
    url: v.string(),                          // Original URL
    title: v.optional(v.string()),            // OpenGraph title
    description: v.optional(v.string()),      // OpenGraph description
    image: v.optional(v.string()),            // OpenGraph image URL
    siteName: v.optional(v.string()),         // OpenGraph site name
    type: v.optional(v.string()),             // OpenGraph type (article, website, etc.)
    domain: v.string(),                       // Extracted domain for display
    favicon: v.optional(v.string()),          // Site favicon URL
    cachedAt: timestampValidator,             // When data was cached - milliseconds since Unix epoch
    expiresAt: timestampValidator,            // When cache expires - milliseconds since Unix epoch
    fetchError: v.optional(v.string())        // Error message if fetch failed
  })
    .index("by_url", ["url"])                 // Get cached data by URL
    .index("by_expires", ["expiresAt"])       // For cleanup of expired cache entries
    .index("by_domain", ["domain"])           // Group by domain for analytics
});
